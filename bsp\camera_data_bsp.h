#ifndef __CAMERA_DATA_BSP_H__
#define __CAMERA_DATA_BSP_H__

#include "bsp_system.h"
#include <string.h>
#include <stdlib.h>

// 摄像头数据处理相关定义
#define CAMERA_WIDTH_PIXELS     320     // 摄像头分辨率宽度
#define CAMERA_HEIGHT_PIXELS    240     // 摄像头分辨率高度
#define CANVAS_WIDTH_PIXELS     240     // 画布区域宽度(像素)
#define CANVAS_HEIGHT_PIXELS    170     // 画布区域高度(像素)
#define CANVAS_OFFSET_X         40      // 画布区域X偏移
#define CANVAS_OFFSET_Y         35      // 画布区域Y偏移

// 物理画布尺寸(毫米)
#define PHYSICAL_CANVAS_WIDTH   50.0f   // 物理画布宽度
#define PHYSICAL_CANVAS_HEIGHT  50.0f   // 物理画布高度

// 数据解析相关定义
#define MAX_LINE_LENGTH         128     // 最大行长度
#define MAX_CANVAS_POINTS       4       // 画布最大点数

// 激光点数据结构
typedef struct {
    uint16_t x;                         // X坐标(像素)
    uint16_t y;                         // Y坐标(像素)
    uint8_t valid;                      // 数据有效标志
    uint32_t timestamp;                 // 时间戳
} LaserPoint_t;

// 画布数据结构
typedef struct {
    uint16_t points[MAX_CANVAS_POINTS][2];  // 四个角点坐标[x,y]
    uint8_t valid;                          // 数据有效标志
    uint32_t timestamp;                     // 时间戳
} CanvasData_t;

// 跟踪模式状态
typedef enum {
    TRACK_MODE_IDLE = 0,                // 空闲状态
    TRACK_MODE_ACTIVE,                  // 激活跟踪
    TRACK_MODE_TRACKING,                // 正在跟踪
    TRACK_MODE_LOST                     // 跟踪丢失
} TrackMode_t;

// 跟踪控制结构
typedef struct {
    TrackMode_t mode;                   // 跟踪模式
    LaserPoint_t current_point;         // 当前激光点
    LaserPoint_t last_point;            // 上一个激光点
    CanvasData_t canvas;                // 画布数据
    uint32_t lost_timeout;              // 丢失超时时间
    uint8_t laser_on;                   // 激光状态
} TrackControl_t;

// 全局变量声明
extern TrackControl_t g_track_control;

// 函数声明

/**
 * @brief 摄像头数据处理模块初始化
 */
void CameraData_Init(void);

/**
 * @brief 解析摄像头数据行
 * @param line 数据行字符串
 * @return 0:成功, -1:失败
 */
int CameraData_ParseLine(const char *line);

/**
 * @brief 解析激光点数据
 * @param data 数据字符串 "violet:(x,y)"
 * @param point 输出的激光点数据
 * @return 0:成功, -1:失败
 */
int CameraData_ParseLaserPoint(const char *data, LaserPoint_t *point);

/**
 * @brief 解析画布数据
 * @param data 数据字符串 "rect:(x1,y1,x2,y2,x3,y3,x4,y4)"
 * @param canvas 输出的画布数据
 * @return 0:成功, -1:失败
 */
int CameraData_ParseCanvas(const char *data, CanvasData_t *canvas);

/**
 * @brief 像素坐标转换为物理坐标
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @param physical_x 输出物理X坐标(mm)
 * @param physical_y 输出物理Y坐标(mm)
 * @return 0:成功, -1:失败
 */
int CameraData_PixelToPhysical(uint16_t pixel_x, uint16_t pixel_y, 
                               float *physical_x, float *physical_y);

/**
 * @brief 检查像素坐标是否在画布范围内
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @return 0:在范围内, -1:超出范围
 */
int CameraData_CheckBounds(uint16_t pixel_x, uint16_t pixel_y);

/**
 * @brief 设置跟踪模式
 * @param mode 跟踪模式
 */
void CameraData_SetTrackMode(TrackMode_t mode);

/**
 * @brief 获取跟踪模式
 * @return 当前跟踪模式
 */
TrackMode_t CameraData_GetTrackMode(void);

/**
 * @brief 处理激光跟踪逻辑
 */
void CameraData_ProcessTracking(void);

/**
 * @brief 获取当前激光点数据
 * @return 激光点数据指针
 */
LaserPoint_t* CameraData_GetCurrentLaserPoint(void);

/**
 * @brief 获取画布数据
 * @return 画布数据指针
 */
CanvasData_t* CameraData_GetCanvasData(void);

#endif /* __CAMERA_DATA_BSP_H__ */
