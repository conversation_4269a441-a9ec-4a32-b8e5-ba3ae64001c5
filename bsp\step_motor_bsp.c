#include "step_motor_bsp.h"
#include "uart_bsp.h"
#include <stdlib.h>

/**
 * @brief 电机初始化函数
 */
void Step_Motor_Init(void)
{
    /* 使能X轴电机 */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* 使能Y轴电机 */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* 初始停止 */
    Step_Motor_Stop();
}

/**
 * @brief 设置XY轴电机速度
 * @param x_percent X轴速度百分比，范围-100到100
 * @param y_percent Y轴速度百分比，范围-100到100
 */
void Step_Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* 限制百分比范围 */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* 设置X轴方向 */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW方向 */
    }
    else
    {
        x_dir = 1;              /* CCW方向 */
        x_percent = -x_percent; /* 取绝对值 */
    }

    /* 设置Y轴方向 */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW方向 */
    }
    else
    {
        y_dir = 1;              /* CCW方向 */
        y_percent = -y_percent; /* 取绝对值 */
    }

    /* 计算实际速度值(百分比转换为RPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

	/* 添加调试打印确认计算 */
     my_printf(&huart1, "X: dir=%d, speed=%u; Y: dir=%d, speed=%u\r\n", x_dir, x_speed, y_dir, y_speed);
	
    /* 控制X轴电机 */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* 控制Y轴电机 */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief 设置XY轴电机速度
 * @param x_rpm X轴目标速度，单位RPM (revolutions per minute)。
 *              支持负值表示反向。
 *              速度精度为0.1RPM。小于0.05 RPM的绝对值将被量化为0。
 * @param y_rpm Y轴目标速度，单位RPM (revolutions per minute)。
 *              支持负值表示反向。
 *              速度精度为0.1RPM。小于0.05 RPM的绝对值将被量化为0。
 */
void Step_Motor_Set_Speed_my(float x_rpm, float y_rpm)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed_scaled, y_speed_scaled; // 速度值，单位为 0.1 RPM
    float abs_x_rpm, abs_y_rpm;

    /* 1. 限制输入RPM范围，确保不超过电机最大物理速度 */
    // 将输入的RPM值钳位在 [-MOTOR_MAX_SPEED, MOTOR_MAX_SPEED] 之间
    if (x_rpm > MOTOR_MAX_SPEED)
    {
        x_rpm = MOTOR_MAX_SPEED;
    }
    else if (x_rpm < -MOTOR_MAX_SPEED)
    {
        x_rpm = -MOTOR_MAX_SPEED;
    }

    if (y_rpm > MOTOR_MAX_SPEED)
    {
        y_rpm = MOTOR_MAX_SPEED;
    }
    else if (y_rpm < -MOTOR_MAX_SPEED)
    {
        y_rpm = -MOTOR_MAX_SPEED;
    }

    /* 2. 处理X轴方向和获取绝对速度 */
    if (x_rpm >= 0.0f)
    {
        x_dir = 0; /* CW方向 (正转) */
        abs_x_rpm = x_rpm;
    }
    else
    {
        x_dir = 1; /* CCW方向 (反转) */
        abs_x_rpm = -x_rpm; /* 取绝对值 */
    }

    /* 3. 处理Y轴方向和获取绝对速度 */
    if (y_rpm >= 0.0f)
    {
        y_dir = 0; /* CW方向 (正转) */
        abs_y_rpm = y_rpm;
    }
    else
    {
        y_dir = 1; /* CCW方向 (反转) */
        abs_y_rpm = -y_rpm; /* 取绝对值 */
    }

    /* 4. 计算实际发送给电机控制器的速度值 (单位为 0.1 RPM) */
    // 将RPM值乘以10，得到以0.1RPM为单位的整数值。
    // 加上0.5f是为了进行四舍五入。
    // 这样，例如 0.04 RPM (0.4 scaled) + 0.5f = 0.9f -> 0 (uint16_t)
    // 0.05 RPM (0.5 scaled) + 0.5f = 1.0f -> 1 (uint16_t)
    x_speed_scaled = (uint16_t)(abs_x_rpm * 10 + 0.5f);
    y_speed_scaled = (uint16_t)(abs_y_rpm * 10 + 0.5f);
    
    // 再次检查计算出的 scaled speed 是否超出 uint16_t 的最大值，
    // 理论上在输入钳位后 (MOTOR_MAX_SPEED * RPM_TO_SCALED_FACTOR) 不会超过 uint16_t，
    // 但作为鲁棒性检查，可以添加此行。
//    uint16_t max_scaled_speed = (uint16_t)(MOTOR_MAX_SPEED * 10 + 0.5f);
//    if (x_speed_scaled > max_scaled_speed) {
//        x_speed_scaled = max_scaled_speed;
//    }
//    if (y_speed_scaled > max_scaled_speed) {
//        y_speed_scaled = max_scaled_speed;
//    }


//    /* 添加调试打印确认计算 */
//    my_printf(&huart1, "X: input_rpm=%.1f, dir=%d, abs_rpm=%.1f, scaled_speed=%u; Y: input_rpm=%.1f, dir=%d, abs_rpm=%.1f, scaled_speed=%u\r\n",
//              x_rpm, x_dir, abs_x_rpm, x_speed_scaled,
//              y_rpm, y_dir, abs_y_rpm, y_speed_scaled);
    
    /* 控制X轴电机 */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* 控制Y轴电机 */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed_scaled, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief 设置XY轴电机移动一段距离（使用位置模式）
 * @param x_distance X轴移动距离（脉冲数），正值为CW方向，负值为CCW方向
 * @param y_distance Y轴移动距离（脉冲数），正值为CW方向，负值为CCW方向
 */
void Step_Motor_Set_Pwm(int32_t x_distance, int32_t y_distance)
{
    uint8_t x_dir, y_dir;
    uint32_t x_clk, y_clk;
    uint16_t speed = MOTOR_MAX_SPEED;  /* 使用最大速度，或根据需要调整 */
    uint8_t acc = MOTOR_ACCEL;        /* 使用预定义加速度 */

    /* 设置X轴方向和脉冲数 */
    if (x_distance >= 0)
    {
        x_dir = 0; /* CW方向 */
        x_clk = (uint32_t)x_distance;
    }
    else
    {
        x_dir = 1; /* CCW方向 */
        x_clk = (uint32_t)(-x_distance); /* 取绝对值 */
    }

    /* 设置Y轴方向和脉冲数 */
    if (y_distance >= 0)
    {
        y_dir = 0; /* CW方向 */
        y_clk = (uint32_t)y_distance;
    }
    else
    {
        y_dir = 1; /* CCW方向 */
        y_clk = (uint32_t)(-y_distance); /* 取绝对值 */
    }

    /* 控制X轴电机（相对运动，不启用绝对模式） */
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, speed, acc, x_clk, false, MOTOR_SYNC_FLAG);

    /* 控制Y轴电机（相对运动，不启用绝对模式） */
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, speed, acc, y_clk, false, MOTOR_SYNC_FLAG);
}


/**
 * @brief 停止所有电机
 */
void Step_Motor_Stop(void)
{
    /* 停止X轴电机 */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* 停止Y轴电机 */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

void step_motor_proc(void)
{

}

/**
 * @brief 位置控制模式：根据像素误差进行精确位置控制
 * @param x_pixels X轴像素误差（正值向右，负值向左）
 * @param y_pixels Y轴像素误差（正值向上，负值向下）
 * @note 实现真正的位置式PID控制策略
 */
void Step_Motor_Set_Position_Increment(float x_pixels, float y_pixels)
{
    // 像素到脉冲的转换系数（根据实际系统标定）
    static float pixels_to_pulses = 5.0f; // 1像素对应5个脉冲

    // 转换为脉冲数
    int32_t x_pulses = (int32_t)(x_pixels * pixels_to_pulses);
    int32_t y_pulses = (int32_t)(y_pixels * pixels_to_pulses);

    // 限制最大脉冲数，避免过大的移动
    int32_t max_pulses = 500; // 最大500个脉冲，约100像素
    if (x_pulses > max_pulses) x_pulses = max_pulses;
    if (x_pulses < -max_pulses) x_pulses = -max_pulses;
    if (y_pulses > max_pulses) y_pulses = max_pulses;
    if (y_pulses < -max_pulses) y_pulses = -max_pulses;

    // 死区处理：如果移动量太小，忽略（减少抖动）
    if (abs(x_pulses) < 3 && abs(y_pulses) < 3) {
        // 调试输出：显示被忽略的小移动
        static uint32_t last_ignore_time = 0;
        uint32_t current_time = HAL_GetTick();
        if (current_time - last_ignore_time >= 1000) {
            my_printf(&huart1, "Small move ignored: pixels(%.1f,%.1f) pulses(%d,%d)\r\n",
                      x_pixels, y_pixels, x_pulses, y_pulses);
            last_ignore_time = current_time;
        }
        return;
    }

    // 确定方向和脉冲数
    uint8_t x_dir = (x_pulses >= 0) ? 0 : 1;  // 正值=0(CW)，负值=1(CCW)
    uint8_t y_dir = (y_pulses >= 0) ? 0 : 1;  // 正值=0(CW)，负值=1(CCW)
    uint32_t x_pulses_abs = (uint32_t)abs(x_pulses);
    uint32_t y_pulses_abs = (uint32_t)abs(y_pulses);

    // 调试输出：显示位置控制信息
    static uint32_t last_debug_time = 0;
    uint32_t current_time = HAL_GetTick();
    if (current_time - last_debug_time >= 500) { // 每500ms输出一次
        my_printf(&huart1, "Pos Control: pixels(%.1f,%.1f) -> pulses(%d,%d)\r\n",
                  x_pixels, y_pixels, x_pulses, y_pulses);
        last_debug_time = current_time;
    }

    // 使用位置控制模式，设置合适的速度
    uint16_t control_speed = MOTOR_MAX_SPEED * 10; // 转换为0.1RPM单位

    if (x_pulses_abs > 0) {
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, control_speed,
                           MOTOR_ACCEL, x_pulses_abs, 0, MOTOR_SYNC_FLAG);
    }

    if (y_pulses_abs > 0) {
        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, control_speed,
                           MOTOR_ACCEL, y_pulses_abs, 0, MOTOR_SYNC_FLAG);
    }
}

/**
 * @brief 按毫米距离移动电机
 * @param dx_mm X轴移动距离(毫米)
 * @param dy_mm Y轴移动距离(毫米)
 * @param speed_percent 速度百分比(1-100)
 */
void Step_Motor_Move_Distance_mm(float dx_mm, float dy_mm, uint8_t speed_percent)
{
    // 毫米到脉冲的转换系数(需要根据实际机械结构调整)
    // 假设每毫米需要100个脉冲(这个值需要根据实际情况校准)
    const float PULSES_PER_MM = 100.0f;

    // 计算需要的脉冲数
    int32_t x_pulses = (int32_t)(dx_mm * PULSES_PER_MM);
    int32_t y_pulses = (int32_t)(dy_mm * PULSES_PER_MM);

    // 限制速度范围
    if (speed_percent < 1) speed_percent = 1;
    if (speed_percent > 100) speed_percent = 100;

    // 计算实际速度(基于最大速度的百分比)
    uint16_t actual_speed = (uint16_t)((speed_percent * MOTOR_MAX_SPEED * 10) / 100);

    // 确定方向
    uint8_t x_dir = (x_pulses >= 0) ? 0 : 1;
    uint8_t y_dir = (y_pulses >= 0) ? 0 : 1;

    // 取绝对值
    uint32_t x_pulses_abs = (uint32_t)abs(x_pulses);
    uint32_t y_pulses_abs = (uint32_t)abs(y_pulses);

    // 调试输出
    my_printf(&huart1, "Move: dx=%.2fmm, dy=%.2fmm, speed=%d%%\r\n",
              dx_mm, dy_mm, speed_percent);
    my_printf(&huart1, "Pulses: X=%ld(dir=%d), Y=%ld(dir=%d)\r\n",
              x_pulses_abs, x_dir, y_pulses_abs, y_dir);

    // 执行移动
    if (x_pulses_abs > 0) {
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, actual_speed,
                           MOTOR_ACCEL, x_pulses_abs, 0, MOTOR_SYNC_FLAG);
    }

    if (y_pulses_abs > 0) {
        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, actual_speed,
                           MOTOR_ACCEL, y_pulses_abs, 0, MOTOR_SYNC_FLAG);
    }

    // 如果两个轴都不需要移动，输出提示
    if (x_pulses_abs == 0 && y_pulses_abs == 0) {
        my_printf(&huart1, "No movement required\r\n");
    }
}

