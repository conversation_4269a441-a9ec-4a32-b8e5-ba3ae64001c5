#include "camera_data_bsp.h"
#include "laser_draw_bsp.h"
#include "uart_bsp.h"

// 全局变量定义
TrackControl_t g_track_control = {0};

/**
 * @brief 摄像头数据处理模块初始化
 */
void CameraData_Init(void)
{
    // 初始化跟踪控制结构
    memset(&g_track_control, 0, sizeof(TrackControl_t));
    g_track_control.mode = TRACK_MODE_IDLE;
    g_track_control.lost_timeout = 1000; // 1秒超时
    
    my_printf(&huart1, "Camera Data Processing Module Initialized\r\n");
}

/**
 * @brief 解析摄像头数据行
 * @param line 数据行字符串
 * @return 0:成功, -1:失败
 */
int CameraData_ParseLine(const char *line)
{
    if (!line) return -1;
    
    // 解析激光点数据
    if (strncmp(line, "violet:", 7) == 0) {
        LaserPoint_t point;
        if (CameraData_ParseLaserPoint(line, &point) == 0) {
            g_track_control.current_point = point;
            g_track_control.current_point.timestamp = HAL_GetTick();
            return 0;
        }
    }
    // 解析画布数据
    else if (strncmp(line, "rect:", 5) == 0) {
        CanvasData_t canvas;
        if (CameraData_ParseCanvas(line, &canvas) == 0) {
            g_track_control.canvas = canvas;
            g_track_control.canvas.timestamp = HAL_GetTick();
            my_printf(&huart1, "Canvas data updated\r\n");
            return 0;
        }
    }
    
    return -1;
}

/**
 * @brief 解析激光点数据
 * @param data 数据字符串 "violet:(x,y)"
 * @param point 输出的激光点数据
 * @return 0:成功, -1:失败
 */
int CameraData_ParseLaserPoint(const char *data, LaserPoint_t *point)
{
    if (!data || !point) return -1;
    
    // 查找括号
    const char *start = strchr(data, '(');
    const char *end = strchr(data, ')');
    if (!start || !end || start >= end) return -1;
    
    // 提取坐标
    char coord_str[32];
    int len = end - start - 1;
    if (len >= sizeof(coord_str)) return -1;
    
    strncpy(coord_str, start + 1, len);
    coord_str[len] = '\0';
    
    // 解析x,y坐标
    char *comma = strchr(coord_str, ',');
    if (!comma) return -1;
    
    *comma = '\0';
    point->x = (uint16_t)atoi(coord_str);
    point->y = (uint16_t)atoi(comma + 1);
    point->valid = 1;
    
    return 0;
}

/**
 * @brief 解析画布数据
 * @param data 数据字符串 "rect:(x1,y1,x2,y2,x3,y3,x4,y4)"
 * @param canvas 输出的画布数据
 * @return 0:成功, -1:失败
 */
int CameraData_ParseCanvas(const char *data, CanvasData_t *canvas)
{
    if (!data || !canvas) return -1;
    
    // 查找括号
    const char *start = strchr(data, '(');
    const char *end = strchr(data, ')');
    if (!start || !end || start >= end) return -1;
    
    // 提取坐标字符串
    char coord_str[128];
    int len = end - start - 1;
    if (len >= sizeof(coord_str)) return -1;
    
    strncpy(coord_str, start + 1, len);
    coord_str[len] = '\0';
    
    // 解析8个坐标值
    char *token = strtok(coord_str, ",");
    int coord_count = 0;
    
    while (token && coord_count < 8) {
        int value = atoi(token);
        canvas->points[coord_count / 2][coord_count % 2] = (uint16_t)value;
        coord_count++;
        token = strtok(NULL, ",");
    }
    
    if (coord_count == 8) {
        canvas->valid = 1;
        return 0;
    }
    
    return -1;
}

/**
 * @brief 像素坐标转换为物理坐标
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @param physical_x 输出物理X坐标(mm)
 * @param physical_y 输出物理Y坐标(mm)
 * @return 0:成功, -1:失败
 */
int CameraData_PixelToPhysical(uint16_t pixel_x, uint16_t pixel_y, 
                               float *physical_x, float *physical_y)
{
    if (!physical_x || !physical_y) return -1;
    
    // 检查边界
    if (CameraData_CheckBounds(pixel_x, pixel_y) != 0) {
        return -1;
    }
    
    // 坐标转换
    // 将像素坐标转换为画布内的相对坐标
    float rel_x = (float)(pixel_x - CANVAS_OFFSET_X);
    float rel_y = (float)(pixel_y - CANVAS_OFFSET_Y);
    
    // 转换为物理坐标(mm)
    *physical_x = rel_x * PHYSICAL_CANVAS_WIDTH / CANVAS_WIDTH_PIXELS;
    *physical_y = rel_y * PHYSICAL_CANVAS_HEIGHT / CANVAS_HEIGHT_PIXELS;
    
    return 0;
}

/**
 * @brief 检查像素坐标是否在画布范围内
 * @param pixel_x 像素X坐标
 * @param pixel_y 像素Y坐标
 * @return 0:在范围内, -1:超出范围
 */
int CameraData_CheckBounds(uint16_t pixel_x, uint16_t pixel_y)
{
    if (pixel_x < CANVAS_OFFSET_X || 
        pixel_x >= (CANVAS_OFFSET_X + CANVAS_WIDTH_PIXELS) ||
        pixel_y < CANVAS_OFFSET_Y || 
        pixel_y >= (CANVAS_OFFSET_Y + CANVAS_HEIGHT_PIXELS)) {
        return -1;
    }
    return 0;
}

/**
 * @brief 设置跟踪模式
 * @param mode 跟踪模式
 */
void CameraData_SetTrackMode(TrackMode_t mode)
{
    g_track_control.mode = mode;
    my_printf(&huart1, "Track mode set to: %d\r\n", mode);
}

/**
 * @brief 获取跟踪模式
 * @return 当前跟踪模式
 */
TrackMode_t CameraData_GetTrackMode(void)
{
    return g_track_control.mode;
}

/**
 * @brief 获取当前激光点数据
 * @return 激光点数据指针
 */
LaserPoint_t* CameraData_GetCurrentLaserPoint(void)
{
    return &g_track_control.current_point;
}

/**
 * @brief 处理激光跟踪逻辑
 */
void CameraData_ProcessTracking(void)
{
    if (g_track_control.mode != TRACK_MODE_ACTIVE &&
        g_track_control.mode != TRACK_MODE_TRACKING) {
        return;
    }

    uint32_t current_time = HAL_GetTick();
    LaserPoint_t *point = &g_track_control.current_point;

    // 检查激光点是否有效且在超时时间内
    if (point->valid &&
        (current_time - point->timestamp) < g_track_control.lost_timeout) {

        // 转换为物理坐标
        float physical_x, physical_y;
        if (CameraData_PixelToPhysical(point->x, point->y,
                                      &physical_x, &physical_y) == 0) {

            // 移动到目标位置并开启激光
            if (LaserDraw_MoveTo(physical_x, physical_y, 1) == 0) {
                g_track_control.mode = TRACK_MODE_TRACKING;
                g_track_control.laser_on = 1;

                // 保存上一个点
                g_track_control.last_point = *point;

                // 调试输出
                static uint32_t last_debug_time = 0;
                if (current_time - last_debug_time >= 500) { // 每500ms输出一次
                    my_printf(&huart1, "Tracking: pixel(%d,%d) -> physical(%.1f,%.1f)\r\n",
                              point->x, point->y, physical_x, physical_y);
                    last_debug_time = current_time;
                }
            }
        }
    } else {
        // 激光点丢失或超时
        if (g_track_control.mode == TRACK_MODE_TRACKING) {
            g_track_control.mode = TRACK_MODE_LOST;
            g_track_control.laser_on = 0;

            // 关闭激光
            Laser_Off();
            my_printf(&huart1, "Laser tracking lost\r\n");
        }
    }
}

/**
 * @brief 获取画布数据
 * @return 画布数据指针
 */
CanvasData_t* CameraData_GetCanvasData(void)
{
    return &g_track_control.canvas;
}
