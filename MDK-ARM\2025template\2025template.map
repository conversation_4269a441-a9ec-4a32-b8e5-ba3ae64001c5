Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) for DMA1_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) for DMA1_Stream5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART2_IRQHandler) for USART2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART3_IRQHandler) for USART3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART4_IRQHandler) for UART4_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART6_IRQHandler) for USART6_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.HAL_UART_RxCpltCallback) refers to uart_bsp.o(i.uart1_process_command) for uart1_process_command
    main.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.HAL_UART_RxCpltCallback) refers to main.o(.data) for .data
    main.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART6_UART_Init) for MX_USART6_UART_Init
    main.o(i.main) refers to usart.o(i.MX_UART4_Init) for MX_UART4_Init
    main.o(i.main) refers to schedule.o(i.schedule_init) for schedule_init
    main.o(i.main) refers to mypid.o(i.PID_INIT) for PID_INIT
    main.o(i.main) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    main.o(i.main) refers to step_motor_bsp.o(i.Step_Motor_Init) for Step_Motor_Init
    main.o(i.main) refers to key_bsp.o(i.key_init) for key_init
    main.o(i.main) refers to laser_draw_bsp.o(i.LaserDraw_Init) for LaserDraw_Init
    main.o(i.main) refers to laser_draw_cmd.o(i.LaserDrawCmd_Init) for LaserDrawCmd_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to schedule.o(i.schedule_run) for schedule_run
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_y
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_y
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_x
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_pi
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pi
    main.o(i.main) refers to uart_bsp.o(.bss) for ringbuffer_pool_test
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to usart.o(.bss) for huart1
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for .bss
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to uart_bsp.o(i.my_printf) for my_printf
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for .bss
    usart.o(i.HAL_UARTEx_RxEventCallback) refers to uart_bsp.o(.bss) for ringbuffer_x
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART4_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_UART4_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART6_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART6_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart.o(i.MX_USART6_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_uart4_rx
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler) refers to usart.o(.bss) for hdma_usart2_rx
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler) refers to usart.o(.bss) for hdma_usart6_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.UART4_IRQHandler) refers to usart.o(.bss) for huart4
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART2_IRQHandler) refers to usart.o(.bss) for huart2
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART3_IRQHandler) refers to usart.o(.bss) for huart3
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    stm32f4xx_it.o(i.USART6_IRQHandler) refers to usart.o(.bss) for huart6
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to main.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c2
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_get) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_init) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_reset) refers to abort.o(.text) for abort
    schedule.o(i.schedule_init) refers to schedule.o(.data) for .data
    schedule.o(i.schedule_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    schedule.o(i.schedule_run) refers to schedule.o(.data) for .data
    schedule.o(.data) refers to uart_bsp.o(i.uart_proc) for uart_proc
    schedule.o(.data) refers to key_bsp.o(i.key_proc) for key_proc
    schedule.o(.data) refers to laser_draw_bsp.o(i.LaserDraw_Process) for LaserDraw_Process
    schedule.o(.data) refers to laser_draw_cmd.o(i.LaserDrawCmd_Process) for LaserDrawCmd_Process
    key_bsp.o(i.key_init) refers to uart_bsp.o(i.my_printf) for my_printf
    key_bsp.o(i.key_init) refers to key_bsp.o(.data) for .data
    key_bsp.o(i.key_init) refers to usart.o(.bss) for huart1
    key_bsp.o(i.key_proc) refers to key_bsp.o(i.key_read) for key_read
    key_bsp.o(i.key_proc) refers to key_bsp.o(.data) for .data
    key_bsp.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    uart_bsp.o(i.check_motor_angle_limits) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.check_motor_angle_limits) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    uart_bsp.o(i.check_motor_angle_limits) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.check_motor_angle_limits) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    uart_bsp.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(i.calc_motor_angle) for calc_motor_angle
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(i.calc_relative_angle) for calc_relative_angle
    uart_bsp.o(i.parse_x_motor_data) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.parse_x_motor_data) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(i.calc_motor_angle) for calc_motor_angle
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(i.calc_relative_angle) for calc_relative_angle
    uart_bsp.o(i.parse_y_motor_data) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.parse_y_motor_data) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.process_reset_command) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.process_reset_command) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    uart_bsp.o(i.process_reset_command) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.process_reset_command) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.save_initial_position) refers to emm_v5.o(i.Emm_V5_Read_Sys_Params) for Emm_V5_Read_Sys_Params
    uart_bsp.o(i.save_initial_position) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.save_initial_position) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.save_initial_position) refers to usart.o(.bss) for huart2
    uart_bsp.o(i.uart1_process_command) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.uart1_process_command) refers to uart_bsp.o(.data) for .data
    uart_bsp.o(i.uart1_process_command) refers to laser_draw_cmd.o(.bss) for g_cmd_buffer
    uart_bsp.o(i.uart1_process_command) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.uart1_process_command) refers to laser_draw_cmd.o(.data) for g_cmd_ready
    uart_bsp.o(i.uart_proc) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    uart_bsp.o(i.uart_proc) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    uart_bsp.o(i.uart_proc) refers to emm_v5.o(i.Emm_V5_Parse_Response) for Emm_V5_Parse_Response
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(i.parse_x_motor_data) for parse_x_motor_data
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(i.my_printf) for my_printf
    uart_bsp.o(i.uart_proc) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(i.parse_y_motor_data) for parse_y_motor_data
    uart_bsp.o(i.uart_proc) refers to laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) for LaserDrawCmd_ParseCameraData
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(.bss) for .bss
    uart_bsp.o(i.uart_proc) refers to usart.o(.bss) for huart1
    uart_bsp.o(i.uart_proc) refers to uart_bsp.o(.data) for .data
    step_motor_bsp.o(i.Step_Motor_Init) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    step_motor_bsp.o(i.Step_Motor_Init) refers to step_motor_bsp.o(i.Step_Motor_Stop) for Step_Motor_Stop
    step_motor_bsp.o(i.Step_Motor_Init) refers to usart.o(.bss) for huart2
    step_motor_bsp.o(i.Step_Motor_Move_Distance_mm) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    step_motor_bsp.o(i.Step_Motor_Move_Distance_mm) refers to uart_bsp.o(i.my_printf) for my_printf
    step_motor_bsp.o(i.Step_Motor_Move_Distance_mm) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    step_motor_bsp.o(i.Step_Motor_Move_Distance_mm) refers to usart.o(.bss) for huart1
    step_motor_bsp.o(i.Step_Motor_Set_Position_Increment) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    step_motor_bsp.o(i.Step_Motor_Set_Position_Increment) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    step_motor_bsp.o(i.Step_Motor_Set_Position_Increment) refers to uart_bsp.o(i.my_printf) for my_printf
    step_motor_bsp.o(i.Step_Motor_Set_Position_Increment) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    step_motor_bsp.o(i.Step_Motor_Set_Position_Increment) refers to step_motor_bsp.o(.data) for .data
    step_motor_bsp.o(i.Step_Motor_Set_Position_Increment) refers to usart.o(.bss) for huart1
    step_motor_bsp.o(i.Step_Motor_Set_Pwm) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    step_motor_bsp.o(i.Step_Motor_Set_Pwm) refers to usart.o(.bss) for huart2
    step_motor_bsp.o(i.Step_Motor_Set_Speed) refers to uart_bsp.o(i.my_printf) for my_printf
    step_motor_bsp.o(i.Step_Motor_Set_Speed) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    step_motor_bsp.o(i.Step_Motor_Set_Speed) refers to usart.o(.bss) for huart1
    step_motor_bsp.o(i.Step_Motor_Set_Speed_my) refers to emm_v5.o(i.Emm_V5_Vel_Control) for Emm_V5_Vel_Control
    step_motor_bsp.o(i.Step_Motor_Set_Speed_my) refers to usart.o(.bss) for huart2
    step_motor_bsp.o(i.Step_Motor_Stop) refers to emm_v5.o(i.Emm_V5_Stop_Now) for Emm_V5_Stop_Now
    step_motor_bsp.o(i.Step_Motor_Stop) refers to usart.o(.bss) for huart2
    laser_draw_bsp.o(i.LaserDraw_Calibrate) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_Calibrate) refers to laser_draw_bsp.o(i.LaserDraw_Home) for LaserDraw_Home
    laser_draw_bsp.o(i.LaserDraw_Calibrate) refers to fminf.o(i.__hardfp_fminf) for __hardfp_fminf
    laser_draw_bsp.o(i.LaserDraw_Calibrate) refers to laser_draw_bsp.o(i.LaserDraw_DrawSquare) for LaserDraw_DrawSquare
    laser_draw_bsp.o(i.LaserDraw_Calibrate) refers to laser_draw_bsp.o(i.LaserDraw_DrawLine) for LaserDraw_DrawLine
    laser_draw_bsp.o(i.LaserDraw_Calibrate) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_CheckBounds) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_CheckBounds) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_CheckBounds) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to laser_draw_bsp.o(i.Laser_On) for Laser_On
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawArc) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to laser_draw_bsp.o(i.Laser_On) for Laser_On
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawCircle) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to laser_draw_bsp.o(i.Laser_On) for Laser_On
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawHeart) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawLine) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawLine) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawLine) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawLine) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawLine) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawSquare) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawSquare) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawSquare) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawSquare) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawSquare) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to laser_draw_bsp.o(i.Laser_On) for Laser_On
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawStar) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_DrawTriangle) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_DrawTriangle) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_DrawTriangle) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_bsp.o(i.LaserDraw_DrawTriangle) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_DrawTriangle) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_EmergencyStop) refers to step_motor_bsp.o(i.Step_Motor_Stop) for Step_Motor_Stop
    laser_draw_bsp.o(i.LaserDraw_EmergencyStop) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_EmergencyStop) refers to laser_draw_bsp.o(i.LaserDraw_StopPath) for LaserDraw_StopPath
    laser_draw_bsp.o(i.LaserDraw_EmergencyStop) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_EmergencyStop) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_EmergencyStop) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_ExecuteMove) refers to laser_draw_bsp.o(i.Laser_On) for Laser_On
    laser_draw_bsp.o(i.LaserDraw_ExecuteMove) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_ExecuteMove) refers to step_motor_bsp.o(i.Step_Motor_Move_Distance_mm) for Step_Motor_Move_Distance_mm
    laser_draw_bsp.o(i.LaserDraw_ExecuteMove) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_GetCurrentX) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_GetCurrentY) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_GetState) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_Home) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_Home) refers to laser_draw_bsp.o(i.LaserDraw_ExecuteMove) for LaserDraw_ExecuteMove
    laser_draw_bsp.o(i.LaserDraw_Home) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_Home) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_Home) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    laser_draw_bsp.o(i.LaserDraw_Init) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_Init) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_Init) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_IsLaserOn) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_MoveRelative) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_bsp.o(i.LaserDraw_MoveRelative) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_MoveTo) refers to laser_draw_bsp.o(i.LaserDraw_CheckBounds) for LaserDraw_CheckBounds
    laser_draw_bsp.o(i.LaserDraw_MoveTo) refers to laser_draw_bsp.o(i.LaserDraw_ExecuteMove) for LaserDraw_ExecuteMove
    laser_draw_bsp.o(i.LaserDraw_Process) refers to laser_draw_bsp.o(i.LaserDraw_ExecuteMove) for LaserDraw_ExecuteMove
    laser_draw_bsp.o(i.LaserDraw_Process) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    laser_draw_bsp.o(i.LaserDraw_Process) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_Process) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_Process) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_SetSpeed) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_SetSpeed) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_SetSpeed) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_StartPath) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_StartPath) refers to laser_draw_bsp.o(i.LaserDraw_StopPath) for LaserDraw_StopPath
    laser_draw_bsp.o(i.LaserDraw_StartPath) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_StartPath) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_StopPath) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.LaserDraw_StopPath) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_StopPath) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.LaserDraw_StopPath) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to laser_draw_bsp.o(i.LaserDraw_DrawSquare) for LaserDraw_DrawSquare
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to laser_draw_bsp.o(i.LaserDraw_DrawCircle) for LaserDraw_DrawCircle
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to laser_draw_bsp.o(i.LaserDraw_DrawTriangle) for LaserDraw_DrawTriangle
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to laser_draw_bsp.o(i.LaserDraw_DrawStar) for LaserDraw_DrawStar
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to laser_draw_bsp.o(i.LaserDraw_DrawHeart) for LaserDraw_DrawHeart
    laser_draw_bsp.o(i.LaserDraw_TestPattern) refers to usart.o(.bss) for huart1
    laser_draw_bsp.o(i.Laser_Off) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    laser_draw_bsp.o(i.Laser_Off) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.Laser_On) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    laser_draw_bsp.o(i.Laser_On) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_bsp.o(i.Laser_Toggle) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_bsp.o(i.Laser_Toggle) refers to laser_draw_bsp.o(i.Laser_On) for Laser_On
    laser_draw_bsp.o(i.Laser_Toggle) refers to laser_draw_bsp.o(.bss) for .bss
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_ShowHelp) for LaserDrawCmd_ShowHelp
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_bsp.o(i.LaserDraw_Home) for LaserDraw_Home
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleMove) for LaserDrawCmd_HandleMove
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleLine) for LaserDrawCmd_HandleLine
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleSquare) for LaserDrawCmd_HandleSquare
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleCircle) for LaserDrawCmd_HandleCircle
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleTriangle) for LaserDrawCmd_HandleTriangle
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleStar) for LaserDrawCmd_HandleStar
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleHeart) for LaserDrawCmd_HandleHeart
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleArc) for LaserDrawCmd_HandleArc
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleSpeed) for LaserDrawCmd_HandleSpeed
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) for LaserDrawCmd_ShowStatus
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_bsp.o(i.LaserDraw_Calibrate) for LaserDraw_Calibrate
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_bsp.o(i.LaserDraw_TestPattern) for LaserDraw_TestPattern
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_bsp.o(i.LaserDraw_StopPath) for LaserDraw_StopPath
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_bsp.o(i.LaserDraw_EmergencyStop) for LaserDraw_EmergencyStop
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStart) for LaserDrawCmd_HandleTrackStart
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop) for LaserDrawCmd_HandleTrackStop
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) for LaserDrawCmd_ShowTrackStatus
    laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleArc) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleArc) refers to laser_draw_bsp.o(i.LaserDraw_DrawArc) for LaserDraw_DrawArc
    laser_draw_cmd.o(i.LaserDrawCmd_HandleArc) refers to laser_draw_cmd.o(.conststring) for .conststring
    laser_draw_cmd.o(i.LaserDrawCmd_HandleArc) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleCircle) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleCircle) refers to laser_draw_bsp.o(i.LaserDraw_DrawCircle) for LaserDraw_DrawCircle
    laser_draw_cmd.o(i.LaserDrawCmd_HandleCircle) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleHeart) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleHeart) refers to laser_draw_bsp.o(i.LaserDraw_DrawHeart) for LaserDraw_DrawHeart
    laser_draw_cmd.o(i.LaserDrawCmd_HandleHeart) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleLine) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleLine) refers to laser_draw_bsp.o(i.LaserDraw_DrawLine) for LaserDraw_DrawLine
    laser_draw_cmd.o(i.LaserDrawCmd_HandleLine) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleMove) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleMove) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_cmd.o(i.LaserDrawCmd_HandleMove) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleSpeed) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleSpeed) refers to laser_draw_bsp.o(i.LaserDraw_SetSpeed) for LaserDraw_SetSpeed
    laser_draw_cmd.o(i.LaserDrawCmd_HandleSpeed) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleSquare) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleSquare) refers to laser_draw_bsp.o(i.LaserDraw_DrawSquare) for LaserDraw_DrawSquare
    laser_draw_cmd.o(i.LaserDrawCmd_HandleSquare) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleStar) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleStar) refers to laser_draw_bsp.o(i.LaserDraw_DrawStar) for LaserDraw_DrawStar
    laser_draw_cmd.o(i.LaserDrawCmd_HandleStar) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStart) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStart) refers to laser_draw_cmd.o(.data) for .data
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStart) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop) refers to laser_draw_cmd.o(.data) for .data
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTriangle) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTriangle) refers to laser_draw_bsp.o(i.LaserDraw_DrawTriangle) for LaserDraw_DrawTriangle
    laser_draw_cmd.o(i.LaserDrawCmd_HandleTriangle) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    laser_draw_cmd.o(i.LaserDrawCmd_Init) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_Init) refers to laser_draw_cmd.o(.bss) for .bss
    laser_draw_cmd.o(i.LaserDrawCmd_Init) refers to laser_draw_cmd.o(.data) for .data
    laser_draw_cmd.o(i.LaserDrawCmd_Init) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to strncmp.o(.text) for strncmp
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to strchr.o(.text) for strchr
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to strncpy.o(.text) for strncpy
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to atoi.o(.text) for atoi
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData) refers to laser_draw_cmd.o(.bss) for .bss
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) refers to strncpy.o(.text) for strncpy
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) refers to strtok.o(.text) for strtok
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) refers to strcmpv7m.o(.text) for strcmp
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) refers to atof.o(i.__hardfp_atof) for __hardfp_atof
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) refers to laser_draw_cmd.o(.constdata) for .constdata
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) for LaserDrawCmd_ProcessTracking
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand) for LaserDrawCmd_ParseCommand
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand) for LaserDrawCmd_ExecuteCommand
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to laser_draw_cmd.o(.data) for .data
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to laser_draw_cmd.o(.bss) for .bss
    laser_draw_cmd.o(i.LaserDrawCmd_Process) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to laser_draw_cmd.o(i.LaserDrawCmd_PixelToPhysical) for LaserDrawCmd_PixelToPhysical
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to laser_draw_bsp.o(i.LaserDraw_MoveTo) for LaserDraw_MoveTo
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to laser_draw_bsp.o(i.Laser_Off) for Laser_Off
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to laser_draw_cmd.o(.data) for .data
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to laser_draw_cmd.o(.bss) for .bss
    laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ShowHelp) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_ShowHelp) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ShowHelp) refers to laser_draw_cmd.o(.constdata) for .constdata
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to laser_draw_bsp.o(i.LaserDraw_GetState) for LaserDraw_GetState
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to laser_draw_bsp.o(i.LaserDraw_GetCurrentY) for LaserDraw_GetCurrentY
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to laser_draw_bsp.o(i.LaserDraw_GetCurrentX) for LaserDraw_GetCurrentX
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to laser_draw_bsp.o(i.LaserDraw_IsLaserOn) for LaserDraw_IsLaserOn
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to laser_draw_cmd.o(.constdata) for .constdata
    laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to uart_bsp.o(i.my_printf) for my_printf
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to laser_draw_cmd.o(i.LaserDrawCmd_PixelToPhysical) for LaserDrawCmd_PixelToPhysical
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to laser_draw_cmd.o(.constdata) for .constdata
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to usart.o(.bss) for huart1
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to laser_draw_cmd.o(.data) for .data
    laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus) refers to laser_draw_cmd.o(.bss) for .bss
    laser_draw_cmd.o(.constdata) refers to laser_draw_cmd.o(.conststring) for .conststring
    hardware_iic.o(i.IIC_Anolog_Normalize) refers to hardware_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(i.IIC_Get_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Digtal) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Offset) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_Get_Single_Anolog) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(i.IIC_ReadByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    hardware_iic.o(i.IIC_ReadByte) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_ReadBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    hardware_iic.o(i.IIC_ReadBytes) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_WriteByte) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    hardware_iic.o(i.IIC_WriteByte) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.IIC_WriteBytes) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    hardware_iic.o(i.IIC_WriteBytes) refers to i2c.o(.bss) for hi2c1
    hardware_iic.o(i.Ping) refers to hardware_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Parse_Response) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    emm_v5.o(i.Emm_V5_Vel_Control) refers to uart_bsp.o(i.my_printf) for my_printf
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(.bss) for huart1
    mypid.o(i.PID_INIT) refers to mypid.o(i.PID_struct_init) for PID_struct_init
    mypid.o(i.PID_INIT) refers to mypid.o(.bss) for .bss
    mypid.o(i.PID_struct_init) refers to mypid.o(i.pid_param_init) for pid_param_init
    mypid.o(i.PID_struct_init) refers to mypid.o(i.pid_reset) for pid_reset
    mypid.o(i.pid_angle_calc) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    mypid.o(i.pid_angle_calc) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    mypid.o(i.pid_angle_calc) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    mypid.o(i.pid_angle_calc) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_calc) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_calc_d) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_calc_i_separation) refers to mypid.o(i.abs_limit) for abs_limit
    mypid.o(i.pid_yaw_calc) refers to mypid.o(i.abs_limit) for abs_limit
    motor_driver_tb6612.o(i.Motor_Create) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver_tb6612.o(i.Motor_Create) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver_tb6612.o(i.Motor_GetState) refers to motor_driver_tb6612.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver_tb6612.o(i.Motor_SetEnable) refers to motor_driver_tb6612.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver_tb6612.o(i.Motor_SetEnable) refers to motor_driver_tb6612.o(i.Motor_Stop) for Motor_Stop
    motor_driver_tb6612.o(i.Motor_SetSpeed) refers to motor_driver_tb6612.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver_tb6612.o(i.Motor_SetSpeed) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    motor_driver_tb6612.o(i.Motor_SetSpeed) refers to motor_driver_tb6612.o(i.Speed_To_PWM) for Speed_To_PWM
    motor_driver_tb6612.o(i.Motor_Stop) refers to motor_driver_tb6612.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_driver_tb6612.o(i.TB6612_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    atoi.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    atoi.o(.text) refers to strtol.o(.text) for strtol
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    strtok.o(.text) refers to strtok_int.o(.text) for __strtok_internal
    strtok.o(.text) refers to strtok.o(.data) for .data
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    strncpy.o(.text) refers to rt_memclr.o(.text) for __aeabi_memclr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    atof.o(i.__hardfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__hardfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__hardfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.__softfp_atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.__softfp_atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.__softfp_atof) refers to strtod.o(.text) for __strtod_int
    atof.o(i.atof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atof.o(i.atof) refers to _rserrno.o(.text) for __read_errno
    atof.o(i.atof) refers to strtod.o(.text) for __strtod_int
    cosf.o(i.__hardfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__hardfp_cosf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf.o(i.__hardfp_cosf) refers to _rserrno.o(.text) for __set_errno
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    cosf.o(i.__hardfp_cosf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf.o(i.__softfp_cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.__softfp_cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf.o(i.cosf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf.o(i.cosf) refers to cosf.o(i.__hardfp_cosf) for __hardfp_cosf
    cosf_x.o(i.____hardfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____hardfp_cosf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    cosf_x.o(i.____hardfp_cosf$lsc) refers to _rserrno.o(.text) for __set_errno
    cosf_x.o(i.____hardfp_cosf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    cosf_x.o(i.____softfp_cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.____softfp_cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    cosf_x.o(i.__cosf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    cosf_x.o(i.__cosf$lsc) refers to cosf_x.o(i.____hardfp_cosf$lsc) for ____hardfp_cosf$lsc
    fmod.o(i.__hardfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__hardfp_fmod) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod.o(i.__hardfp_fmod) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod.o(i.__hardfp_fmod) refers to _rserrno.o(.text) for __set_errno
    fmod.o(i.__hardfp_fmod) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    fmod.o(i.__softfp_fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.__softfp_fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod.o(i.fmod) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod.o(i.fmod) refers to fmod.o(i.__hardfp_fmod) for __hardfp_fmod
    fmod_x.o(i.____hardfp_fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.____hardfp_fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.____hardfp_fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.____hardfp_fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    fmod_x.o(i.____softfp_fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.____softfp_fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.____softfp_fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.____softfp_fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    fmod_x.o(i.__fmod$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmod_x.o(i.__fmod$lsc) refers to drem_clz.o(x$fpl$drem) for _drem
    fmod_x.o(i.__fmod$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    fmod_x.o(i.__fmod$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__hardfp_sinf) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf.o(i.__hardfp_sinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    sinf.o(i.__hardfp_sinf) refers to _rserrno.o(.text) for __set_errno
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    sinf.o(i.__hardfp_sinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf.o(i.__softfp_sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.__softfp_sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf.o(i.sinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf.o(i.sinf) refers to sinf.o(i.__hardfp_sinf) for __hardfp_sinf
    sinf_x.o(i.____hardfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____hardfp_sinf$lsc) refers to rredf.o(i.__mathlib_rredf2) for __mathlib_rredf2
    sinf_x.o(i.____hardfp_sinf$lsc) refers to _rserrno.o(.text) for __set_errno
    sinf_x.o(i.____hardfp_sinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    sinf_x.o(i.____softfp_sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.____softfp_sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    sinf_x.o(i.__sinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sinf_x.o(i.__sinf$lsc) refers to sinf_x.o(i.____hardfp_sinf$lsc) for ____hardfp_sinf$lsc
    fminf.o(i.__hardfp_fminf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fminf.o(i.__hardfp_fminf) refers to fcmp4.o(x$fpl$fcmp4) for __ARM_fcmp4
    fminf.o(i.fminf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fminf.o(i.fminf) refers to fcmp4.o(x$fpl$fcmp4) for __ARM_fcmp4
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    strtok_int.o(.text) refers to strspn.o(.text) for strspn
    strtok_int.o(.text) refers to strcspn.o(.text) for strcspn
    rt_memclr.o(.text) refers to rt_memclr_w.o(.text) for _memset_w
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drem_clz.o(x$fpl$drem) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp4.o(x$fpl$fcmp4) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp4.o(x$fpl$fcmp4) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp4.o(x$fpl$fcmp4) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fcmp4.o(x$fpl$fcmp4e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp4.o(x$fpl$fcmp4e) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp4.o(x$fpl$fcmp4e) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rredf.o(i.__mathlib_rredf2) refers to rredf.o(.constdata) for .constdata
    rredf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    narrow.o(i.__hardfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__hardfp___mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__hardfp___mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__hardfp___mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__hardfp___mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__hardfp___mathlib_tofloat) for __hardfp___mathlib_tofloat
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    frexp.o(i.__hardfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__hardfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    ldexp.o(i.__hardfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__hardfp_ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.__hardfp_ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.__hardfp_ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.__hardfp_ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to ldexp.o(i.__hardfp_ldexp) for __hardfp_ldexp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to _rserrno.o(.text) for __set_errno
    ldexp_x.o(i.____hardfp_ldexp$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    ldexp_x.o(i.____softfp_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____softfp_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.____support_ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.____support_ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    ldexp_x.o(i.__ldexp$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp_x.o(i.__ldexp$lsc) refers to ldexp_x.o(i.____hardfp_ldexp$lsc) for ____hardfp_ldexp$lsc
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (28 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (72 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (268 bytes).
    Removing usart.o(.bss), (64 bytes).
    Removing usart.o(.bss), (32 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit), (300 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed), (46 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite), (156 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout), (86 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout), (112 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout), (86 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_Delay), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (124 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (52 bytes).
    Removing oled.o(i.OLED_Clear), (52 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_Init), (48 bytes).
    Removing oled.o(i.OLED_Set_Position), (34 bytes).
    Removing oled.o(i.OLED_ShowChar), (136 bytes).
    Removing oled.o(i.OLED_ShowFloat), (266 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (76 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (114 bytes).
    Removing oled.o(i.OLED_ShowPic), (64 bytes).
    Removing oled.o(i.OLED_ShowStr), (54 bytes).
    Removing oled.o(i.OLED_Write_cmd), (36 bytes).
    Removing oled.o(i.OLED_Write_data), (36 bytes).
    Removing oled.o(.constdata), (2712 bytes).
    Removing oled.o(.data), (22 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).
    Removing schedule.o(.rev16_text), (4 bytes).
    Removing schedule.o(.revsh_text), (4 bytes).
    Removing schedule.o(.rrx_text), (6 bytes).
    Removing oled_bsp.o(i.oled_proc), (2 bytes).
    Removing key_bsp.o(.rev16_text), (4 bytes).
    Removing key_bsp.o(.revsh_text), (4 bytes).
    Removing key_bsp.o(.rrx_text), (6 bytes).
    Removing uart_bsp.o(.rev16_text), (4 bytes).
    Removing uart_bsp.o(.revsh_text), (4 bytes).
    Removing uart_bsp.o(.rrx_text), (6 bytes).
    Removing uart_bsp.o(i.check_motor_angle_limits), (268 bytes).
    Removing uart_bsp.o(i.process_reset_command), (188 bytes).
    Removing uart_bsp.o(i.save_initial_position), (92 bytes).
    Removing uart_bsp.o(.bss), (64 bytes).
    Removing step_motor_bsp.o(.rev16_text), (4 bytes).
    Removing step_motor_bsp.o(.revsh_text), (4 bytes).
    Removing step_motor_bsp.o(.rrx_text), (6 bytes).
    Removing step_motor_bsp.o(i.Step_Motor_Set_Position_Increment), (468 bytes).
    Removing step_motor_bsp.o(i.Step_Motor_Set_Pwm), (84 bytes).
    Removing step_motor_bsp.o(i.Step_Motor_Set_Speed), (200 bytes).
    Removing step_motor_bsp.o(i.Step_Motor_Set_Speed_my), (196 bytes).
    Removing step_motor_bsp.o(i.step_motor_proc), (2 bytes).
    Removing step_motor_bsp.o(.data), (12 bytes).
    Removing laser_draw_bsp.o(.rev16_text), (4 bytes).
    Removing laser_draw_bsp.o(.revsh_text), (4 bytes).
    Removing laser_draw_bsp.o(.rrx_text), (6 bytes).
    Removing laser_draw_bsp.o(i.LaserDraw_MoveRelative), (28 bytes).
    Removing laser_draw_bsp.o(i.LaserDraw_StartPath), (144 bytes).
    Removing laser_draw_bsp.o(i.Laser_Toggle), (20 bytes).
    Removing laser_draw_cmd.o(.rev16_text), (4 bytes).
    Removing laser_draw_cmd.o(.revsh_text), (4 bytes).
    Removing laser_draw_cmd.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(.rev16_text), (4 bytes).
    Removing hardware_iic.o(.revsh_text), (4 bytes).
    Removing hardware_iic.o(.rrx_text), (6 bytes).
    Removing hardware_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing hardware_iic.o(i.IIC_Get_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_Get_Digtal), (20 bytes).
    Removing hardware_iic.o(i.IIC_Get_Offset), (24 bytes).
    Removing hardware_iic.o(i.IIC_Get_Single_Anolog), (22 bytes).
    Removing hardware_iic.o(i.IIC_ReadByte), (32 bytes).
    Removing hardware_iic.o(i.IIC_ReadBytes), (36 bytes).
    Removing hardware_iic.o(i.IIC_WriteByte), (44 bytes).
    Removing hardware_iic.o(i.IIC_WriteBytes), (36 bytes).
    Removing hardware_iic.o(i.Ping), (30 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (158 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (142 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (48 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (120 bytes).
    Removing mypid.o(.rev16_text), (4 bytes).
    Removing mypid.o(.revsh_text), (4 bytes).
    Removing mypid.o(.rrx_text), (6 bytes).
    Removing mypid.o(i.abs_limit), (42 bytes).
    Removing mypid.o(i.pid_angle_calc), (356 bytes).
    Removing mypid.o(i.pid_calc), (404 bytes).
    Removing mypid.o(i.pid_calc_d), (256 bytes).
    Removing mypid.o(i.pid_calc_i_separation), (420 bytes).
    Removing mypid.o(i.pid_clear), (28 bytes).
    Removing mypid.o(i.pid_yaw_calc), (320 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing mypid.o(.data), (4 bytes).
    Removing motor_driver_tb6612.o(.rev16_text), (4 bytes).
    Removing motor_driver_tb6612.o(.revsh_text), (4 bytes).
    Removing motor_driver_tb6612.o(.rrx_text), (6 bytes).
    Removing motor_driver_tb6612.o(i.Motor_Create), (144 bytes).
    Removing motor_driver_tb6612.o(i.Motor_GetState), (18 bytes).
    Removing motor_driver_tb6612.o(i.Motor_SetEnable), (32 bytes).
    Removing motor_driver_tb6612.o(i.Motor_SetSpeed), (288 bytes).
    Removing motor_driver_tb6612.o(i.Motor_Stop), (6 bytes).
    Removing motor_driver_tb6612.o(i.Motor_ValidateParams), (16 bytes).
    Removing motor_driver_tb6612.o(i.Speed_To_PWM), (24 bytes).
    Removing motor_driver_tb6612.o(i.TB6612_Init), (24 bytes).

585 unused section(s) (total 51374 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strncpy.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  atoi.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strchr.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strcspn.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strncmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strtok_int.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strspn.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/drem.s                          0x00000000   Number         0  drem_clz.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fcmp4.s                         0x00000000   Number         0  fcmp4.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atof.c                        0x00000000   Number         0  atof.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf.o ABSOLUTE
    ../mathlib/cosf.c                        0x00000000   Number         0  cosf_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fmax.c                        0x00000000   Number         0  fminf.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod.o ABSOLUTE
    ../mathlib/fmod.c                        0x00000000   Number         0  fmod_x.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp_x.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rredf.c                       0x00000000   Number         0  rredf.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf_x.o ABSOLUTE
    ../mathlib/sinf.c                        0x00000000   Number         0  sinf.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\OLED\oled.c                           0x00000000   Number         0  oled.o ABSOLUTE
    ..\TB6612\motor_driver_tb6612.c          0x00000000   Number         0  motor_driver_tb6612.o ABSOLUTE
    ..\\OLED\\oled.c                         0x00000000   Number         0  oled.o ABSOLUTE
    ..\\TB6612\\motor_driver_tb6612.c        0x00000000   Number         0  motor_driver_tb6612.o ABSOLUTE
    ..\\app\\Emm_V5.c                        0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\\app\\hardware_iic.c                  0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\\app\\mypid.c                         0x00000000   Number         0  mypid.o ABSOLUTE
    ..\\bsp\\key_bsp.c                       0x00000000   Number         0  key_bsp.o ABSOLUTE
    ..\\bsp\\laser_draw_bsp.c                0x00000000   Number         0  laser_draw_bsp.o ABSOLUTE
    ..\\bsp\\laser_draw_cmd.c                0x00000000   Number         0  laser_draw_cmd.o ABSOLUTE
    ..\\bsp\\schedule.c                      0x00000000   Number         0  schedule.o ABSOLUTE
    ..\\bsp\\step_motor_bsp.c                0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    ..\\bsp\\uart_bsp.c                      0x00000000   Number         0  uart_bsp.o ABSOLUTE
    ..\app\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\app\hardware_iic.c                    0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\app\mypid.c                           0x00000000   Number         0  mypid.o ABSOLUTE
    ..\bsp\key_bsp.c                         0x00000000   Number         0  key_bsp.o ABSOLUTE
    ..\bsp\laser_draw_bsp.c                  0x00000000   Number         0  laser_draw_bsp.o ABSOLUTE
    ..\bsp\laser_draw_cmd.c                  0x00000000   Number         0  laser_draw_cmd.o ABSOLUTE
    ..\bsp\oled_bsp.c                        0x00000000   Number         0  oled_bsp.o ABSOLUTE
    ..\bsp\pi_bsp.c                          0x00000000   Number         0  pi_bsp.o ABSOLUTE
    ..\bsp\schedule.c                        0x00000000   Number         0  schedule.o ABSOLUTE
    ..\bsp\step_motor_bsp.c                  0x00000000   Number         0  step_motor_bsp.o ABSOLUTE
    ..\bsp\uart_bsp.c                        0x00000000   Number         0  uart_bsp.o ABSOLUTE
    ..\ringbuffer\ringbuffer.c               0x00000000   Number         0  ringbuffer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000314   Section      238  lludivv7m.o(.text)
    .text                                    0x08000404   Section        0  vsnprintf.o(.text)
    .text                                    0x08000438   Section        0  atoi.o(.text)
    .text                                    0x08000452   Section        0  abort.o(.text)
    .text                                    0x08000468   Section        0  strtok.o(.text)
    .text                                    0x08000474   Section        0  strchr.o(.text)
    .text                                    0x08000488   Section        0  strncmp.o(.text)
    .text                                    0x0800051e   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080005a8   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x0800060c   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800065a   Section       86  strncpy.o(.text)
    .text                                    0x080006b0   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000730   Section        0  heapauxi.o(.text)
    .text                                    0x08000738   Section        0  sys_exit.o(.text)
    .text                                    0x08000744   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x0800074c   Section        0  _rserrno.o(.text)
    .text                                    0x08000762   Section        0  _printf_pad.o(.text)
    .text                                    0x080007b0   Section        0  _printf_truncate.o(.text)
    .text                                    0x080007d4   Section        0  _printf_str.o(.text)
    .text                                    0x08000828   Section        0  _printf_dec.o(.text)
    .text                                    0x080008a0   Section        0  _printf_charcount.o(.text)
    .text                                    0x080008c8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080008c9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080008f8   Section        0  _sputc.o(.text)
    .text                                    0x08000902   Section        0  _snputc.o(.text)
    .text                                    0x08000914   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080009d0   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000a4c   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000a4d   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000abc   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000abd   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000b50   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000cd8   Section        0  strtod.o(.text)
    _local_sscanf                            0x08000cd9   Thumb Code    60  strtod.o(.text)
    .text                                    0x08000d7c   Section        0  strtol.o(.text)
    .text                                    0x08000dec   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08000dfc   Section        0  strtok_int.o(.text)
    .text                                    0x08000e40   Section       68  rt_memclr.o(.text)
    .text                                    0x08000e84   Section        8  libspace.o(.text)
    .text                                    0x08000e8c   Section        2  use_no_semi.o(.text)
    .text                                    0x08000e8e   Section        0  indicate_semi.o(.text)
    .text                                    0x08000e90   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08000ea0   Section      138  lludiv10.o(.text)
    .text                                    0x08000f2a   Section        0  isspace.o(.text)
    .text                                    0x08000f3c   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000fee   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000ff1   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x0800140c   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001708   Section        0  _printf_char.o(.text)
    .text                                    0x08001734   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001760   Section        0  _sgetc.o(.text)
    .text                                    0x080017a0   Section        0  _strtoul.o(.text)
    .text                                    0x0800183e   Section        0  _wcrtomb.o(.text)
    .text                                    0x0800187e   Section        0  defsig_exit.o(.text)
    .text                                    0x08001888   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x080018b8   Section        0  strcspn.o(.text)
    .text                                    0x080018d8   Section        0  strspn.o(.text)
    .text                                    0x080018f4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001940   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001948   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080019c8   Section        0  _chval.o(.text)
    .text                                    0x080019e4   Section        0  scanf_fp.o(.text)
    _fp_value                                0x080019e5   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x08001edc   Section        0  bigflt0.o(.text)
    .text                                    0x08001fc0   Section        0  exit.o(.text)
    .text                                    0x08001fd2   Section        0  defsig_general.o(.text)
    .text                                    0x08002004   Section        0  sys_wrch.o(.text)
    .text                                    0x08002014   Section        0  scanf_hexfp.o(.text)
    .text                                    0x08002334   Section        0  scanf_infnan.o(.text)
    .text                                    0x08002468   Section       38  llshl.o(.text)
    CL$$btod_d2e                             0x0800248e   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080024cc   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002512   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002572   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x080028ac   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08002930   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002a0c   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x08002a36   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x08002a60   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x08002a8a   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x08002ab4   Section      580  btod.o(CL$$btod_mult_common)
    i.BusFault_Handler                       0x08002cf8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x08002cfc   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA1_Stream2_IRQHandler                0x08002d08   Section        0  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    i.DMA1_Stream5_IRQHandler                0x08002d14   Section        0  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x08002d20   Section        0  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08002d2c   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08002d38   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08002d39   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08002d60   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08002d61   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002db4   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002db5   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x08002ddc   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x08002dde   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Parse_Response                  0x08002e16   Section        0  emm_v5.o(i.Emm_V5_Parse_Response)
    i.Emm_V5_Pos_Control                     0x08003002   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Emm_V5_Stop_Now                        0x08003072   Section        0  emm_v5.o(i.Emm_V5_Stop_Now)
    i.Error_Handler                          0x080030a6   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x080030aa   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x0800313c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08003160   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08003300   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x080033d4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_GPIO_Init                          0x08003444   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x08003634   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x0800363e   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08003648   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08003654   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_MspInit                        0x080037dc   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08003888   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003898   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x080038cc   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x0800390c   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x0800393c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003958   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003998   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x080039bc   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08003af0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08003b10   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08003b30   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003b94   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x08003f00   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x08003f28   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08003f7c   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x0800400c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08004068   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_ConfigClockSource              0x08004090   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x0800416c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08004210   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_MspPostInit                    0x080042b4   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_PWM_ConfigChannel              0x08004308   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x080043d4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x0800442e   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x08004430   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x0800447c   Section        0  usart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08004558   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800455c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080047dc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08004840   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08004b88   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08004ba4   Section        0  main.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08004bd4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08004bd6   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08004c76   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08004c78   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.LaserDrawCmd_ExecuteCommand            0x08004c7c   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand)
    i.LaserDrawCmd_HandleArc                 0x08004dfc   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleArc)
    i.LaserDrawCmd_HandleCircle              0x08004e24   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleCircle)
    i.LaserDrawCmd_HandleHeart               0x08004e7c   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleHeart)
    i.LaserDrawCmd_HandleLine                0x08004ed0   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleLine)
    i.LaserDrawCmd_HandleMove                0x08004f28   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleMove)
    i.LaserDrawCmd_HandleSpeed               0x08004f7c   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleSpeed)
    i.LaserDrawCmd_HandleSquare              0x08004ff8   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleSquare)
    i.LaserDrawCmd_HandleStar                0x08005050   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleStar)
    i.LaserDrawCmd_HandleTrackStart          0x080050a4   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStart)
    i.LaserDrawCmd_HandleTrackStop           0x080050dc   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop)
    i.LaserDrawCmd_HandleTriangle            0x08005118   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_HandleTriangle)
    i.LaserDrawCmd_Init                      0x08005170   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_Init)
    i.LaserDrawCmd_ParseCameraData           0x08005210   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData)
    i.LaserDrawCmd_ParseCommand              0x080052d8   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand)
    i.LaserDrawCmd_PixelToPhysical           0x08005384   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_PixelToPhysical)
    LaserDrawCmd_PixelToPhysical             0x08005385   Thumb Code    86  laser_draw_cmd.o(i.LaserDrawCmd_PixelToPhysical)
    i.LaserDrawCmd_Process                   0x080053e8   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_Process)
    i.LaserDrawCmd_ProcessTracking           0x08005480   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking)
    i.LaserDrawCmd_ShowHelp                  0x08005578   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ShowHelp)
    i.LaserDrawCmd_ShowStatus                0x08005808   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus)
    i.LaserDrawCmd_ShowTrackStatus           0x08005914   Section        0  laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus)
    i.LaserDraw_Calibrate                    0x08005ac4   Section        0  laser_draw_bsp.o(i.LaserDraw_Calibrate)
    i.LaserDraw_CheckBounds                  0x08005b8c   Section        0  laser_draw_bsp.o(i.LaserDraw_CheckBounds)
    i.LaserDraw_DrawArc                      0x08005c2c   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawArc)
    i.LaserDraw_DrawCircle                   0x08005dc4   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawCircle)
    i.LaserDraw_DrawHeart                    0x08005ebc   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawHeart)
    i.LaserDraw_DrawLine                     0x08006080   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawLine)
    i.LaserDraw_DrawSquare                   0x0800613c   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawSquare)
    i.LaserDraw_DrawStar                     0x08006238   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawStar)
    i.LaserDraw_DrawTriangle                 0x08006348   Section        0  laser_draw_bsp.o(i.LaserDraw_DrawTriangle)
    i.LaserDraw_EmergencyStop                0x08006458   Section        0  laser_draw_bsp.o(i.LaserDraw_EmergencyStop)
    i.LaserDraw_ExecuteMove                  0x0800649c   Section        0  laser_draw_bsp.o(i.LaserDraw_ExecuteMove)
    LaserDraw_ExecuteMove                    0x0800649d   Thumb Code    76  laser_draw_bsp.o(i.LaserDraw_ExecuteMove)
    i.LaserDraw_GetCurrentX                  0x080064ec   Section        0  laser_draw_bsp.o(i.LaserDraw_GetCurrentX)
    i.LaserDraw_GetCurrentY                  0x080064f8   Section        0  laser_draw_bsp.o(i.LaserDraw_GetCurrentY)
    i.LaserDraw_GetState                     0x08006504   Section        0  laser_draw_bsp.o(i.LaserDraw_GetState)
    i.LaserDraw_Home                         0x08006510   Section        0  laser_draw_bsp.o(i.LaserDraw_Home)
    i.LaserDraw_Init                         0x08006568   Section        0  laser_draw_bsp.o(i.LaserDraw_Init)
    i.LaserDraw_IsLaserOn                    0x0800663c   Section        0  laser_draw_bsp.o(i.LaserDraw_IsLaserOn)
    i.LaserDraw_MoveTo                       0x08006648   Section        0  laser_draw_bsp.o(i.LaserDraw_MoveTo)
    i.LaserDraw_Process                      0x08006680   Section        0  laser_draw_bsp.o(i.LaserDraw_Process)
    i.LaserDraw_SetSpeed                     0x0800670c   Section        0  laser_draw_bsp.o(i.LaserDraw_SetSpeed)
    i.LaserDraw_StopPath                     0x08006760   Section        0  laser_draw_bsp.o(i.LaserDraw_StopPath)
    i.LaserDraw_TestPattern                  0x08006794   Section        0  laser_draw_bsp.o(i.LaserDraw_TestPattern)
    i.Laser_Off                              0x08006860   Section        0  laser_draw_bsp.o(i.Laser_Off)
    i.Laser_On                               0x0800687c   Section        0  laser_draw_bsp.o(i.Laser_On)
    i.MX_DMA_Init                            0x08006898   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08006914   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08006a34   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x08006a74   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x08006ab4   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM3_Init                           0x08006b8c   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x08006bf8   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART4_Init                          0x08006c64   Section        0  usart.o(i.MX_UART4_Init)
    i.MX_UART5_Init                          0x08006cb4   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08006d04   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08006d3c   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08006d8c   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.MX_USART6_UART_Init                    0x08006dc4   Section        0  usart.o(i.MX_USART6_UART_Init)
    i.MemManage_Handler                      0x08006e14   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08006e16   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PID_INIT                               0x08006e18   Section        0  mypid.o(i.PID_INIT)
    i.PID_struct_init                        0x08006ef0   Section        0  mypid.o(i.PID_struct_init)
    i.PendSV_Handler                         0x08006f34   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08006f36   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Step_Motor_Init                        0x08006f38   Section        0  step_motor_bsp.o(i.Step_Motor_Init)
    i.Step_Motor_Move_Distance_mm            0x08006f64   Section        0  step_motor_bsp.o(i.Step_Motor_Move_Distance_mm)
    i.Step_Motor_Stop                        0x080070c0   Section        0  step_motor_bsp.o(i.Step_Motor_Stop)
    i.SysTick_Handler                        0x080070e4   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080070e8   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08007178   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM_Base_SetConfig                     0x08007188   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08007258   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x0800726c   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x0800726d   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x0800727c   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x0800727d   Thumb Code    88  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x080072dc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x08007348   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x08007349   Thumb Code    96  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x080073b0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x080073b1   Thumb Code    70  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08007400   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08007401   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x08007422   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x08007423   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART4_IRQHandler                       0x08007448   Section        0  stm32f4xx_it.o(i.UART4_IRQHandler)
    i.UART5_IRQHandler                       0x08007474   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x080074a0   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080074a1   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x080074ae   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x080074af   Thumb Code    74  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x080074f8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x080074f9   Thumb Code   134  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x0800757e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x0800757f   Thumb Code    30  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x0800759c   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800759d   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTxTransfer                     0x080075ea   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080075eb   Thumb Code    28  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08007606   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08007607   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080076c8   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080076c9   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x080077d4   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Start_Receive_IT                  0x08007874   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x080078aa   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x080078ab   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x0800791c   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.USART2_IRQHandler                      0x08007928   Section        0  stm32f4xx_it.o(i.USART2_IRQHandler)
    i.USART3_IRQHandler                      0x08007954   Section        0  stm32f4xx_it.o(i.USART3_IRQHandler)
    i.USART6_IRQHandler                      0x08007960   Section        0  stm32f4xx_it.o(i.USART6_IRQHandler)
    i.UsageFault_Handler                     0x0800798c   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x0800798e   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x080079be   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__NVIC_SetPriority                     0x080079e4   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080079e5   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp___mathlib_tofloat             0x08007a08   Section        0  narrow.o(i.__hardfp___mathlib_tofloat)
    i.__hardfp_atof                          0x08007b00   Section        0  atof.o(i.__hardfp_atof)
    i.__hardfp_cosf                          0x08007b38   Section        0  cosf.o(i.__hardfp_cosf)
    i.__hardfp_fminf                         0x08007c88   Section        0  fminf.o(i.__hardfp_fminf)
    i.__hardfp_ldexp                         0x08007cd0   Section        0  ldexp.o(i.__hardfp_ldexp)
    i.__hardfp_sinf                          0x08007da0   Section        0  sinf.o(i.__hardfp_sinf)
    i.__mathlib_dbl_overflow                 0x08007f30   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08007f50   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_flt_infnan                   0x08007f70   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_invalid                  0x08007f78   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_underflow                0x08007f88   Section        0  funder.o(i.__mathlib_flt_underflow)
    i.__mathlib_narrow                       0x08007f98   Section        0  narrow.o(i.__mathlib_narrow)
    i.__mathlib_rredf2                       0x08007fac   Section        0  rredf.o(i.__mathlib_rredf2)
    i.__support_ldexp                        0x08008100   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08008114   Section        0  __printf_wp.o(i._is_digit)
    i.calc_motor_angle                       0x08008124   Section        0  uart_bsp.o(i.calc_motor_angle)
    i.calc_relative_angle                    0x08008150   Section        0  uart_bsp.o(i.calc_relative_angle)
    i.frexp                                  0x08008198   Section        0  frexp.o(i.frexp)
    i.key_init                               0x08008224   Section        0  key_bsp.o(i.key_init)
    i.key_proc                               0x08008320   Section        0  key_bsp.o(i.key_proc)
    i.key_read                               0x08008340   Section        0  key_bsp.o(i.key_read)
    i.main                                   0x08008380   Section        0  main.o(i.main)
    i.my_printf                              0x08008438   Section        0  uart_bsp.o(i.my_printf)
    i.parse_x_motor_data                     0x0800846c   Section        0  uart_bsp.o(i.parse_x_motor_data)
    i.parse_y_motor_data                     0x08008760   Section        0  uart_bsp.o(i.parse_y_motor_data)
    i.pid_param_init                         0x08008a54   Section        0  mypid.o(i.pid_param_init)
    pid_param_init                           0x08008a55   Thumb Code    14  mypid.o(i.pid_param_init)
    i.pid_reset                              0x08008a64   Section        0  mypid.o(i.pid_reset)
    pid_reset                                0x08008a65   Thumb Code    26  mypid.o(i.pid_reset)
    i.rt_ringbuffer_data_len                 0x08008a84   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08008ab4   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08008b28   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x08008b58   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x08008bd0   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x08008bd1   Thumb Code    32  ringbuffer.o(i.rt_ringbuffer_status)
    i.schedule_init                          0x08008bf0   Section        0  schedule.o(i.schedule_init)
    i.schedule_run                           0x08008bfc   Section        0  schedule.o(i.schedule_run)
    i.uart1_process_command                  0x08008c38   Section        0  uart_bsp.o(i.uart1_process_command)
    i.uart_proc                              0x08008cc4   Section        0  uart_bsp.o(i.uart_proc)
    locale$$code                             0x08008e5c   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08008e88   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08008eb4   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08008eb4   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$dcheck1                            0x08008f18   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x08008f18   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08008f28   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08008f28   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$deqf                               0x08008f40   Section      120  deqf.o(x$fpl$deqf)
    $v0                                      0x08008f40   Number         0  deqf.o(x$fpl$deqf)
    x$fpl$dleqf                              0x08008fb8   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08008fb8   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08009030   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08009030   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08009184   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08009184   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08009220   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08009220   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x0800922c   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x0800922c   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$f2d                                0x08009298   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08009298   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fcmp4                              0x080092f0   Section       82  fcmp4.o(x$fpl$fcmp4)
    $v0                                      0x080092f0   Number         0  fcmp4.o(x$fpl$fcmp4)
    x$fpl$fcmpinf                            0x08009342   Section       24  fcmpi.o(x$fpl$fcmpinf)
    $v0                                      0x08009342   Number         0  fcmpi.o(x$fpl$fcmpinf)
    x$fpl$fnaninf                            0x0800935a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800935a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080093e6   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080093e6   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x080093f0   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x080093f0   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$ieeestatus                         0x080093fa   Section        6  istatus.o(x$fpl$ieeestatus)
    $v0                                      0x080093fa   Number         0  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x08009400   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x08009400   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08009404   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08009404   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08009408   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08009408   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x0800946c   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x0800946c   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x080094c8   Section        4  scanf1.o(x$fpl$scanf1)
    $v0                                      0x080094c8   Number         0  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x080094cc   Section        8  scanf2.o(x$fpl$scanf2)
    $v0                                      0x080094cc   Number         0  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x080094d4   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x080094d4   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08009504   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x08009504   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x08009504   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800950c   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800951c   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08009524   Section     2724  laser_draw_cmd.o(.constdata)
    command_table                            0x08009524   Data        2688  laser_draw_cmd.o(.constdata)
    .constdata                               0x08009fc8   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08009fc8   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08009fd0   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08009fd0   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08009fe4   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08009ff8   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08009ff8   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800a00c   Section       32  rredf.o(.constdata)
    twooverpi                                0x0800a00c   Data          32  rredf.o(.constdata)
    .constdata                               0x0800a02c   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800a02c   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800a03f   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800a054   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800a054   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800a090   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800a0e8   Section      141  laser_draw_cmd.o(.conststring)
    c$$dinf                                  0x0800a198   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x0800a1a0   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x0800a1a8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800a1ac   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800a1b4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800a1c0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800a1c2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800a1c3   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800a1c4   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800a1c4   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800a1c8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800a1d0   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800a2d4   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        1  main.o(.data)
    uart1_rx_data                            0x20000000   Data           1  main.o(.data)
    .data                                    0x20000004   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section       52  schedule.o(.data)
    schedule_task                            0x20000018   Data          48  schedule.o(.data)
    .data                                    0x20000048   Section       12  key_bsp.o(.data)
    reset_key_pressed                        0x2000004c   Data           1  key_bsp.o(.data)
    reset_key_press_time                     0x20000050   Data           4  key_bsp.o(.data)
    .data                                    0x20000054   Section       48  uart_bsp.o(.data)
    cmd_index                                0x2000005c   Data           1  uart_bsp.o(.data)
    line_buffer_idx                          0x20000060   Data           4  uart_bsp.o(.data)
    .data                                    0x20000084   Section       12  laser_draw_cmd.o(.data)
    g_track_mode                             0x20000085   Data           1  laser_draw_cmd.o(.data)
    g_track_lost_timeout                     0x20000088   Data           4  laser_draw_cmd.o(.data)
    last_debug_time                          0x2000008c   Data           4  laser_draw_cmd.o(.data)
    .data                                    0x20000090   Section        4  strtok.o(.data)
    _strtok_saves1                           0x20000090   Data           4  strtok.o(.data)
    .bss                                     0x20000094   Section      168  i2c.o(.bss)
    .bss                                     0x2000013c   Section      216  tim.o(.bss)
    .bss                                     0x20000214   Section     1168  usart.o(.bss)
    .bss                                     0x200006a4   Section      356  uart_bsp.o(.bss)
    line_buffer                              0x20000788   Data         128  uart_bsp.o(.bss)
    .bss                                     0x20000808   Section       12  uart_bsp.o(.bss)
    .bss                                     0x20000814   Section       64  uart_bsp.o(.bss)
    .bss                                     0x20000854   Section       64  uart_bsp.o(.bss)
    .bss                                     0x20000894   Section       64  uart_bsp.o(.bss)
    .bss                                     0x200008d4   Section       64  uart_bsp.o(.bss)
    .bss                                     0x20000914   Section       28  laser_draw_bsp.o(.bss)
    .bss                                     0x20000930   Section      140  laser_draw_cmd.o(.bss)
    g_current_laser_point                    0x200009b0   Data          12  laser_draw_cmd.o(.bss)
    .bss                                     0x200009bc   Section      480  mypid.o(.bss)
    .bss                                     0x20000b9c   Section       96  libspace.o(.bss)
    HEAP                                     0x20000c00   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000c00   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000e00   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000e00   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20001200   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000315   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000315   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000405   Thumb Code    48  vsnprintf.o(.text)
    atoi                                     0x08000439   Thumb Code    26  atoi.o(.text)
    abort                                    0x08000453   Thumb Code    22  abort.o(.text)
    strtok                                   0x08000469   Thumb Code     6  strtok.o(.text)
    strchr                                   0x08000475   Thumb Code    20  strchr.o(.text)
    strncmp                                  0x08000489   Thumb Code   150  strncmp.o(.text)
    __aeabi_memcpy                           0x0800051f   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x0800051f   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x08000585   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memcpy4                          0x080005a9   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x080005a9   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x080005a9   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x080005f1   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memclr4                          0x0800060d   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x0800060d   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x0800060d   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x08000611   Thumb Code     0  rt_memclr_w.o(.text)
    strncpy                                  0x0800065b   Thumb Code    86  strncpy.o(.text)
    strcmp                                   0x080006b1   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000731   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000733   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000735   Thumb Code     2  heapauxi.o(.text)
    _sys_exit                                0x08000739   Thumb Code     8  sys_exit.o(.text)
    __aeabi_errno_addr                       0x08000745   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000745   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000745   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x0800074d   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000757   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08000763   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800078f   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080007b1   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080007c3   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x080007d5   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000829   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x080008a1   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x080008d3   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080008f9   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000903   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000915   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080009d1   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000a4d   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000a8f   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000aa7   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000abd   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000b13   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000b2f   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000b3b   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000b51   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __strtod_int                             0x08000d15   Thumb Code    90  strtod.o(.text)
    strtol                                   0x08000d7d   Thumb Code   112  strtol.o(.text)
    __rt_SIGABRT                             0x08000ded   Thumb Code    14  defsig_abrt_outer.o(.text)
    __strtok_internal                        0x08000dfd   Thumb Code    64  strtok_int.o(.text)
    __aeabi_memclr                           0x08000e41   Thumb Code     0  rt_memclr.o(.text)
    __rt_memclr                              0x08000e41   Thumb Code    68  rt_memclr.o(.text)
    _memset                                  0x08000e45   Thumb Code     0  rt_memclr.o(.text)
    __user_libspace                          0x08000e85   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000e85   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000e85   Thumb Code     0  libspace.o(.text)
    __I$use$semihosting                      0x08000e8d   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000e8d   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000e8f   Thumb Code     0  indicate_semi.o(.text)
    __rt_ctype_table                         0x08000e91   Thumb Code    16  rt_ctype_table.o(.text)
    _ll_udiv10                               0x08000ea1   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08000f2b   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08000f3d   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000fef   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080011a1   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x0800140d   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001709   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x0800171d   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x0800172d   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001735   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001749   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001759   Thumb Code     8  _printf_wchar.o(.text)
    _sgetc                                   0x08001761   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x0800177f   Thumb Code    34  _sgetc.o(.text)
    _strtoul                                 0x080017a1   Thumb Code   158  _strtoul.o(.text)
    _wcrtomb                                 0x0800183f   Thumb Code    64  _wcrtomb.o(.text)
    __sig_exit                               0x0800187f   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001889   Thumb Code    14  defsig_abrt_inner.o(.text)
    strcspn                                  0x080018b9   Thumb Code    32  strcspn.o(.text)
    strspn                                   0x080018d9   Thumb Code    28  strspn.o(.text)
    __user_setup_stackheap                   0x080018f5   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_locale                              0x08001941   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001949   Thumb Code   112  _printf_fp_infnan.o(.text)
    _chval                                   0x080019c9   Thumb Code    28  _chval.o(.text)
    _scanf_really_real                       0x08001c31   Thumb Code   684  scanf_fp.o(.text)
    _btod_etento                             0x08001edd   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001fc1   Thumb Code    18  exit.o(.text)
    __default_signal_display                 0x08001fd3   Thumb Code    50  defsig_general.o(.text)
    _ttywrch                                 0x08002005   Thumb Code    14  sys_wrch.o(.text)
    _scanf_really_hex_real                   0x08002015   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x08002335   Thumb Code   292  scanf_infnan.o(.text)
    __aeabi_llsl                             0x08002469   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08002469   Thumb Code    38  llshl.o(.text)
    _btod_d2e                                0x0800248f   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080024cd   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002513   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002573   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x080028ad   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x08002931   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002a0d   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x08002a37   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x08002a61   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x08002a8b   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x08002ab5   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BusFault_Handler                         0x08002cf9   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x08002cfd   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA1_Stream2_IRQHandler                  0x08002d09   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream2_IRQHandler)
    DMA1_Stream5_IRQHandler                  0x08002d15   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream5_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08002d21   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08002d2d   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08002ddd   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x08002ddf   Thumb Code    56  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Parse_Response                    0x08002e17   Thumb Code   492  emm_v5.o(i.Emm_V5_Parse_Response)
    Emm_V5_Pos_Control                       0x08003003   Thumb Code   112  emm_v5.o(i.Emm_V5_Pos_Control)
    Emm_V5_Stop_Now                          0x08003073   Thumb Code    52  emm_v5.o(i.Emm_V5_Stop_Now)
    Error_Handler                            0x080030a7   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x080030ab   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x0800313d   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08003161   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08003301   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x080033d5   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_GPIO_Init                            0x08003445   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x08003635   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x0800363f   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08003649   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08003655   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_MspInit                          0x080037dd   Thumb Code   154  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08003889   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003899   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x080038cd   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x0800390d   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800393d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003959   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003999   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080039bd   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08003af1   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08003b11   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08003b31   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003b95   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08003f01   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_ConfigBreakDeadTime            0x08003f29   Thumb Code    84  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08003f7d   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x0800400d   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08004069   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_ConfigClockSource                0x08004091   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x0800416d   Thumb Code   164  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08004211   Thumb Code   142  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_MspPostInit                      0x080042b5   Thumb Code    72  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_PWM_ConfigChannel                0x08004309   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x080043d5   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x0800442f   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_UARTEx_ReceiveToIdle_DMA             0x08004431   Thumb Code    74  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x0800447d   Thumb Code   146  usart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08004559   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800455d   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080047dd   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08004841   Thumb Code   782  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08004b89   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08004ba5   Thumb Code    34  main.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08004bd5   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08004bd7   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08004c77   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08004c79   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    LaserDrawCmd_ExecuteCommand              0x08004c7d   Thumb Code   272  laser_draw_cmd.o(i.LaserDrawCmd_ExecuteCommand)
    LaserDrawCmd_HandleArc                   0x08004dfd   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleArc)
    LaserDrawCmd_HandleCircle                0x08004e25   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleCircle)
    LaserDrawCmd_HandleHeart                 0x08004e7d   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleHeart)
    LaserDrawCmd_HandleLine                  0x08004ed1   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleLine)
    LaserDrawCmd_HandleMove                  0x08004f29   Thumb Code    34  laser_draw_cmd.o(i.LaserDrawCmd_HandleMove)
    LaserDrawCmd_HandleSpeed                 0x08004f7d   Thumb Code    56  laser_draw_cmd.o(i.LaserDrawCmd_HandleSpeed)
    LaserDrawCmd_HandleSquare                0x08004ff9   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleSquare)
    LaserDrawCmd_HandleStar                  0x08005051   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleStar)
    LaserDrawCmd_HandleTrackStart            0x080050a5   Thumb Code    20  laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStart)
    LaserDrawCmd_HandleTrackStop             0x080050dd   Thumb Code    24  laser_draw_cmd.o(i.LaserDrawCmd_HandleTrackStop)
    LaserDrawCmd_HandleTriangle              0x08005119   Thumb Code    32  laser_draw_cmd.o(i.LaserDrawCmd_HandleTriangle)
    LaserDrawCmd_Init                        0x08005171   Thumb Code    44  laser_draw_cmd.o(i.LaserDrawCmd_Init)
    LaserDrawCmd_ParseCameraData             0x08005211   Thumb Code   148  laser_draw_cmd.o(i.LaserDrawCmd_ParseCameraData)
    LaserDrawCmd_ParseCommand                0x080052d9   Thumb Code   160  laser_draw_cmd.o(i.LaserDrawCmd_ParseCommand)
    LaserDrawCmd_Process                     0x080053e9   Thumb Code    66  laser_draw_cmd.o(i.LaserDrawCmd_Process)
    LaserDrawCmd_ProcessTracking             0x08005481   Thumb Code   164  laser_draw_cmd.o(i.LaserDrawCmd_ProcessTracking)
    LaserDrawCmd_ShowHelp                    0x08005579   Thumb Code   136  laser_draw_cmd.o(i.LaserDrawCmd_ShowHelp)
    LaserDrawCmd_ShowStatus                  0x08005809   Thumb Code   134  laser_draw_cmd.o(i.LaserDrawCmd_ShowStatus)
    LaserDrawCmd_ShowTrackStatus             0x08005915   Thumb Code   160  laser_draw_cmd.o(i.LaserDrawCmd_ShowTrackStatus)
    LaserDraw_Calibrate                      0x08005ac5   Thumb Code   130  laser_draw_bsp.o(i.LaserDraw_Calibrate)
    LaserDraw_CheckBounds                    0x08005b8d   Thumb Code   106  laser_draw_bsp.o(i.LaserDraw_CheckBounds)
    LaserDraw_DrawArc                        0x08005c2d   Thumb Code   336  laser_draw_bsp.o(i.LaserDraw_DrawArc)
    LaserDraw_DrawCircle                     0x08005dc5   Thumb Code   192  laser_draw_bsp.o(i.LaserDraw_DrawCircle)
    LaserDraw_DrawHeart                      0x08005ebd   Thumb Code   392  laser_draw_bsp.o(i.LaserDraw_DrawHeart)
    LaserDraw_DrawLine                       0x08006081   Thumb Code   138  laser_draw_bsp.o(i.LaserDraw_DrawLine)
    LaserDraw_DrawSquare                     0x0800613d   Thumb Code   198  laser_draw_bsp.o(i.LaserDraw_DrawSquare)
    LaserDraw_DrawStar                       0x08006239   Thumb Code   214  laser_draw_bsp.o(i.LaserDraw_DrawStar)
    LaserDraw_DrawTriangle                   0x08006349   Thumb Code   214  laser_draw_bsp.o(i.LaserDraw_DrawTriangle)
    LaserDraw_EmergencyStop                  0x08006459   Thumb Code    32  laser_draw_bsp.o(i.LaserDraw_EmergencyStop)
    LaserDraw_GetCurrentX                    0x080064ed   Thumb Code     8  laser_draw_bsp.o(i.LaserDraw_GetCurrentX)
    LaserDraw_GetCurrentY                    0x080064f9   Thumb Code     8  laser_draw_bsp.o(i.LaserDraw_GetCurrentY)
    LaserDraw_GetState                       0x08006505   Thumb Code     6  laser_draw_bsp.o(i.LaserDraw_GetState)
    LaserDraw_Home                           0x08006511   Thumb Code    52  laser_draw_bsp.o(i.LaserDraw_Home)
    LaserDraw_Init                           0x08006569   Thumb Code    92  laser_draw_bsp.o(i.LaserDraw_Init)
    LaserDraw_IsLaserOn                      0x0800663d   Thumb Code     6  laser_draw_bsp.o(i.LaserDraw_IsLaserOn)
    LaserDraw_MoveTo                         0x08006649   Thumb Code    54  laser_draw_bsp.o(i.LaserDraw_MoveTo)
    LaserDraw_Process                        0x08006681   Thumb Code   100  laser_draw_bsp.o(i.LaserDraw_Process)
    LaserDraw_SetSpeed                       0x0800670d   Thumb Code    38  laser_draw_bsp.o(i.LaserDraw_SetSpeed)
    LaserDraw_StopPath                       0x08006761   Thumb Code    26  laser_draw_bsp.o(i.LaserDraw_StopPath)
    LaserDraw_TestPattern                    0x08006795   Thumb Code   134  laser_draw_bsp.o(i.LaserDraw_TestPattern)
    Laser_Off                                0x08006861   Thumb Code    20  laser_draw_bsp.o(i.Laser_Off)
    Laser_On                                 0x0800687d   Thumb Code    20  laser_draw_bsp.o(i.Laser_On)
    MX_DMA_Init                              0x08006899   Thumb Code   120  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08006915   Thumb Code   268  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08006a35   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x08006a75   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x08006ab5   Thumb Code   206  tim.o(i.MX_TIM1_Init)
    MX_TIM3_Init                             0x08006b8d   Thumb Code    98  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x08006bf9   Thumb Code    98  tim.o(i.MX_TIM4_Init)
    MX_UART4_Init                            0x08006c65   Thumb Code    66  usart.o(i.MX_UART4_Init)
    MX_UART5_Init                            0x08006cb5   Thumb Code    66  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08006d05   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08006d3d   Thumb Code    64  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08006d8d   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    MX_USART6_UART_Init                      0x08006dc5   Thumb Code    64  usart.o(i.MX_USART6_UART_Init)
    MemManage_Handler                        0x08006e15   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08006e17   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PID_INIT                                 0x08006e19   Thumb Code   186  mypid.o(i.PID_INIT)
    PID_struct_init                          0x08006ef1   Thumb Code    60  mypid.o(i.PID_struct_init)
    PendSV_Handler                           0x08006f35   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08006f37   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Step_Motor_Init                          0x08006f39   Thumb Code    34  step_motor_bsp.o(i.Step_Motor_Init)
    Step_Motor_Move_Distance_mm              0x08006f65   Thumb Code   224  step_motor_bsp.o(i.Step_Motor_Move_Distance_mm)
    Step_Motor_Stop                          0x080070c1   Thumb Code    26  step_motor_bsp.o(i.Step_Motor_Stop)
    SysTick_Handler                          0x080070e5   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080070e9   Thumb Code   136  main.o(i.SystemClock_Config)
    SystemInit                               0x08007179   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    TIM_Base_SetConfig                       0x08007189   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08007259   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x080072dd   Thumb Code    98  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART4_IRQHandler                         0x08007449   Thumb Code    32  stm32f4xx_it.o(i.UART4_IRQHandler)
    UART5_IRQHandler                         0x08007475   Thumb Code    32  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x080077d5   Thumb Code   146  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    UART_Start_Receive_IT                    0x08007875   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x0800791d   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    USART2_IRQHandler                        0x08007929   Thumb Code    32  stm32f4xx_it.o(i.USART2_IRQHandler)
    USART3_IRQHandler                        0x08007955   Thumb Code     6  stm32f4xx_it.o(i.USART3_IRQHandler)
    USART6_IRQHandler                        0x08007961   Thumb Code    32  stm32f4xx_it.o(i.USART6_IRQHandler)
    UsageFault_Handler                       0x0800798d   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x0800798f   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x080079bf   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp___mathlib_tofloat               0x08007a09   Thumb Code   232  narrow.o(i.__hardfp___mathlib_tofloat)
    __hardfp_atof                            0x08007b01   Thumb Code    44  atof.o(i.__hardfp_atof)
    __hardfp_cosf                            0x08007b39   Thumb Code   280  cosf.o(i.__hardfp_cosf)
    __hardfp_fminf                           0x08007c89   Thumb Code    72  fminf.o(i.__hardfp_fminf)
    __hardfp_ldexp                           0x08007cd1   Thumb Code   200  ldexp.o(i.__hardfp_ldexp)
    __hardfp_sinf                            0x08007da1   Thumb Code   344  sinf.o(i.__hardfp_sinf)
    __mathlib_dbl_overflow                   0x08007f31   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08007f51   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_flt_infnan                     0x08007f71   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_invalid                    0x08007f79   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_underflow                  0x08007f89   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    __mathlib_narrow                         0x08007f99   Thumb Code    18  narrow.o(i.__mathlib_narrow)
    __mathlib_rredf2                         0x08007fad   Thumb Code   316  rredf.o(i.__mathlib_rredf2)
    __support_ldexp                          0x08008101   Thumb Code    20  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08008115   Thumb Code    14  __printf_wp.o(i._is_digit)
    calc_motor_angle                         0x08008125   Thumb Code    36  uart_bsp.o(i.calc_motor_angle)
    calc_relative_angle                      0x08008151   Thumb Code    62  uart_bsp.o(i.calc_relative_angle)
    frexp                                    0x08008199   Thumb Code   118  frexp.o(i.frexp)
    key_init                                 0x08008225   Thumb Code    62  key_bsp.o(i.key_init)
    key_proc                                 0x08008321   Thumb Code    28  key_bsp.o(i.key_proc)
    key_read                                 0x08008341   Thumb Code    58  key_bsp.o(i.key_read)
    main                                     0x08008381   Thumb Code   142  main.o(i.main)
    my_printf                                0x08008439   Thumb Code    52  uart_bsp.o(i.my_printf)
    parse_x_motor_data                       0x0800846d   Thumb Code   268  uart_bsp.o(i.parse_x_motor_data)
    parse_y_motor_data                       0x08008761   Thumb Code   268  uart_bsp.o(i.parse_y_motor_data)
    rt_ringbuffer_data_len                   0x08008a85   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08008ab5   Thumb Code   116  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08008b29   Thumb Code    48  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x08008b59   Thumb Code   120  ringbuffer.o(i.rt_ringbuffer_put)
    schedule_init                            0x08008bf1   Thumb Code     8  schedule.o(i.schedule_init)
    schedule_run                             0x08008bfd   Thumb Code    56  schedule.o(i.schedule_run)
    uart1_process_command                    0x08008c39   Thumb Code   116  uart_bsp.o(i.uart1_process_command)
    uart_proc                                0x08008cc5   Thumb Code   272  uart_bsp.o(i.uart_proc)
    _get_lc_numeric                          0x08008e5d   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08008e89   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08008eb5   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08008eb5   Thumb Code    98  d2f.o(x$fpl$d2f)
    __fpl_dcheck_NaN1                        0x08008f19   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08008f29   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_cdcmpeq                          0x08008f41   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x08008f41   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_cdcmple                          0x08008fb9   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08008fb9   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x0800901b   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08009031   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08009031   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08009185   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08009221   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x0800922d   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x0800922d   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_f2d                              0x08009299   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08009299   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fcmp4                                  0x080092f1   Thumb Code    24  fcmp4.o(x$fpl$fcmp4)
    __ARM_fcmp4                              0x08009309   Thumb Code     0  fcmp4.o(x$fpl$fcmp4)
    _fcmp4                                   0x08009309   Thumb Code    58  fcmp4.o(x$fpl$fcmp4)
    __fpl_fcmp_Inf                           0x08009343   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __fpl_fnaninf                            0x0800935b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080093e7   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080093ef   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080093ef   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x080093f1   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __ieee_status                            0x080093fb   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x08009401   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08009405   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08009409   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x0800946d   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x080094c9   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x080094cd   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x080094d1   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x080094d5   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08009504   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x0800950c   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800951c   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x0800a178   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800a198   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x0800a198   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x0800a198   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x0800a198   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x0800a198   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x0800a198   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x0800a1a0   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x0800a1d1   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000004   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x2000000c   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    task_num                                 0x20000014   Data           1  schedule.o(.data)
    key_val                                  0x20000048   Data           1  key_bsp.o(.data)
    key_old                                  0x20000049   Data           1  key_bsp.o(.data)
    key_down                                 0x2000004a   Data           1  key_bsp.o(.data)
    key_up                                   0x2000004b   Data           1  key_bsp.o(.data)
    x_angle_limit_flag                       0x20000054   Data           1  uart_bsp.o(.data)
    y_angle_limit_flag                       0x20000055   Data           1  uart_bsp.o(.data)
    motor_angle_limit_check_enabled          0x20000056   Data           1  uart_bsp.o(.data)
    x_reference_initialized                  0x20000057   Data           1  uart_bsp.o(.data)
    y_reference_initialized                  0x20000058   Data           1  uart_bsp.o(.data)
    x_initial_direction                      0x20000059   Data           1  uart_bsp.o(.data)
    y_initial_direction                      0x2000005a   Data           1  uart_bsp.o(.data)
    initial_position_saved                   0x2000005b   Data           1  uart_bsp.o(.data)
    x_motor_angle                            0x20000064   Data           4  uart_bsp.o(.data)
    y_motor_angle                            0x20000068   Data           4  uart_bsp.o(.data)
    x_reference_position                     0x2000006c   Data           4  uart_bsp.o(.data)
    y_reference_position                     0x20000070   Data           4  uart_bsp.o(.data)
    x_relative_angle                         0x20000074   Data           4  uart_bsp.o(.data)
    y_relative_angle                         0x20000078   Data           4  uart_bsp.o(.data)
    x_initial_position                       0x2000007c   Data           4  uart_bsp.o(.data)
    y_initial_position                       0x20000080   Data           4  uart_bsp.o(.data)
    g_cmd_ready                              0x20000084   Data           1  laser_draw_cmd.o(.data)
    hi2c1                                    0x20000094   Data          84  i2c.o(.bss)
    hi2c2                                    0x200000e8   Data          84  i2c.o(.bss)
    htim1                                    0x2000013c   Data          72  tim.o(.bss)
    htim3                                    0x20000184   Data          72  tim.o(.bss)
    htim4                                    0x200001cc   Data          72  tim.o(.bss)
    motor_x_buf                              0x20000214   Data          64  usart.o(.bss)
    motor_y_buf                              0x20000254   Data          64  usart.o(.bss)
    pi_rx_buf                                0x20000294   Data          64  usart.o(.bss)
    test_buf                                 0x200002d4   Data          64  usart.o(.bss)
    huart4                                   0x20000314   Data          72  usart.o(.bss)
    huart5                                   0x2000035c   Data          72  usart.o(.bss)
    huart1                                   0x200003a4   Data          72  usart.o(.bss)
    huart2                                   0x200003ec   Data          72  usart.o(.bss)
    huart3                                   0x20000434   Data          72  usart.o(.bss)
    huart6                                   0x2000047c   Data          72  usart.o(.bss)
    hdma_uart4_rx                            0x200004c4   Data          96  usart.o(.bss)
    hdma_uart5_rx                            0x20000524   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x20000584   Data          96  usart.o(.bss)
    hdma_usart2_rx                           0x200005e4   Data          96  usart.o(.bss)
    hdma_usart6_rx                           0x20000644   Data          96  usart.o(.bss)
    ringbuffer_x                             0x200006a4   Data          12  uart_bsp.o(.bss)
    ringbuffer_y                             0x200006b0   Data          12  uart_bsp.o(.bss)
    ringbuffer_test                          0x200006bc   Data          12  uart_bsp.o(.bss)
    output_buffer_x                          0x200006c8   Data          64  uart_bsp.o(.bss)
    output_buffer_y                          0x20000708   Data          64  uart_bsp.o(.bss)
    output_buffer_test                       0x20000748   Data          64  uart_bsp.o(.bss)
    ringbuffer_pi                            0x20000808   Data          12  uart_bsp.o(.bss)
    ringbuffer_pool_x                        0x20000814   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_y                        0x20000854   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_pi                       0x20000894   Data          64  uart_bsp.o(.bss)
    ringbuffer_pool_test                     0x200008d4   Data          64  uart_bsp.o(.bss)
    g_laser_draw                             0x20000914   Data          28  laser_draw_bsp.o(.bss)
    g_cmd_buffer                             0x20000930   Data         128  laser_draw_cmd.o(.bss)
    pid_x                                    0x200009bc   Data          80  mypid.o(.bss)
    pid_y                                    0x20000a0c   Data          80  mypid.o(.bss)
    pid_speed_left                           0x20000a5c   Data          80  mypid.o(.bss)
    pid_speed_right                          0x20000aac   Data          80  mypid.o(.bss)
    pid_location_left                        0x20000afc   Data          80  mypid.o(.bss)
    pid_location_right                       0x20000b4c   Data          80  mypid.o(.bss)
    __libspace_start                         0x20000b9c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000bfc   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000a368, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000a2d4, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         4862  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         5404    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         5406    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         5408    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         5011    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         5000    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         5002    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         5007    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         5008    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         5009    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         5010    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         5015    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         5004    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         5005    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         5006    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         5003    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         5001    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         5012    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         5013    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         5014    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         5019    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         5020    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         5016    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         4998    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         4999    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         5017    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         5018    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         5139    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         5223    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         5224    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5227    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5230    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5232    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         5234    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         5235    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x00000000   Code   RO         5237    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x0000000c   Code   RO         5238    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         5239    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         5241    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x0000000a   Code   RO         5242    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5243    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5245    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5247    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5249    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5251    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5253    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5255    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5257    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5261    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5263    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5265    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         5267    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         5268    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         5354    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5376    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5378    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5380    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5383    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5386    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5388    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         5391    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         5392    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         4916    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         5102    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         5114    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         5104    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         5105    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         5107    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         5108    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         5275    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         5304    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         5305    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         5306    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000314   0x08000314   0x000000ee   Code   RO         4835    .text               c_w.l(lludivv7m.o)
    0x08000402   0x08000402   0x00000002   PAD
    0x08000404   0x08000404   0x00000034   Code   RO         4837    .text               c_w.l(vsnprintf.o)
    0x08000438   0x08000438   0x0000001a   Code   RO         4839    .text               c_w.l(atoi.o)
    0x08000452   0x08000452   0x00000016   Code   RO         4841    .text               c_w.l(abort.o)
    0x08000468   0x08000468   0x0000000c   Code   RO         4843    .text               c_w.l(strtok.o)
    0x08000474   0x08000474   0x00000014   Code   RO         4846    .text               c_w.l(strchr.o)
    0x08000488   0x08000488   0x00000096   Code   RO         4848    .text               c_w.l(strncmp.o)
    0x0800051e   0x0800051e   0x0000008a   Code   RO         4850    .text               c_w.l(rt_memcpy_v6.o)
    0x080005a8   0x080005a8   0x00000064   Code   RO         4852    .text               c_w.l(rt_memcpy_w.o)
    0x0800060c   0x0800060c   0x0000004e   Code   RO         4854    .text               c_w.l(rt_memclr_w.o)
    0x0800065a   0x0800065a   0x00000056   Code   RO         4856    .text               c_w.l(strncpy.o)
    0x080006b0   0x080006b0   0x00000080   Code   RO         4858    .text               c_w.l(strcmpv7m.o)
    0x08000730   0x08000730   0x00000006   Code   RO         4860    .text               c_w.l(heapauxi.o)
    0x08000736   0x08000736   0x00000002   PAD
    0x08000738   0x08000738   0x0000000c   Code   RO         4914    .text               c_w.l(sys_exit.o)
    0x08000744   0x08000744   0x00000008   Code   RO         4924    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x0800074c   0x0800074c   0x00000016   Code   RO         4926    .text               c_w.l(_rserrno.o)
    0x08000762   0x08000762   0x0000004e   Code   RO         4930    .text               c_w.l(_printf_pad.o)
    0x080007b0   0x080007b0   0x00000024   Code   RO         4932    .text               c_w.l(_printf_truncate.o)
    0x080007d4   0x080007d4   0x00000052   Code   RO         4934    .text               c_w.l(_printf_str.o)
    0x08000826   0x08000826   0x00000002   PAD
    0x08000828   0x08000828   0x00000078   Code   RO         4936    .text               c_w.l(_printf_dec.o)
    0x080008a0   0x080008a0   0x00000028   Code   RO         4938    .text               c_w.l(_printf_charcount.o)
    0x080008c8   0x080008c8   0x00000030   Code   RO         4940    .text               c_w.l(_printf_char_common.o)
    0x080008f8   0x080008f8   0x0000000a   Code   RO         4942    .text               c_w.l(_sputc.o)
    0x08000902   0x08000902   0x00000010   Code   RO         4944    .text               c_w.l(_snputc.o)
    0x08000912   0x08000912   0x00000002   PAD
    0x08000914   0x08000914   0x000000bc   Code   RO         4946    .text               c_w.l(_printf_wctomb.o)
    0x080009d0   0x080009d0   0x0000007c   Code   RO         4949    .text               c_w.l(_printf_longlong_dec.o)
    0x08000a4c   0x08000a4c   0x00000070   Code   RO         4955    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000abc   0x08000abc   0x00000094   Code   RO         4975    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000b50   0x08000b50   0x00000188   Code   RO         4995    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000cd8   0x08000cd8   0x000000a4   Code   RO         5021    .text               c_w.l(strtod.o)
    0x08000d7c   0x08000d7c   0x00000070   Code   RO         5023    .text               c_w.l(strtol.o)
    0x08000dec   0x08000dec   0x0000000e   Code   RO         5025    .text               c_w.l(defsig_abrt_outer.o)
    0x08000dfa   0x08000dfa   0x00000002   PAD
    0x08000dfc   0x08000dfc   0x00000044   Code   RO         5029    .text               c_w.l(strtok_int.o)
    0x08000e40   0x08000e40   0x00000044   Code   RO         5031    .text               c_w.l(rt_memclr.o)
    0x08000e84   0x08000e84   0x00000008   Code   RO         5095    .text               c_w.l(libspace.o)
    0x08000e8c   0x08000e8c   0x00000002   Code   RO         5098    .text               c_w.l(use_no_semi.o)
    0x08000e8e   0x08000e8e   0x00000000   Code   RO         5100    .text               c_w.l(indicate_semi.o)
    0x08000e8e   0x08000e8e   0x00000002   PAD
    0x08000e90   0x08000e90   0x00000010   Code   RO         5116    .text               c_w.l(rt_ctype_table.o)
    0x08000ea0   0x08000ea0   0x0000008a   Code   RO         5122    .text               c_w.l(lludiv10.o)
    0x08000f2a   0x08000f2a   0x00000012   Code   RO         5124    .text               c_w.l(isspace.o)
    0x08000f3c   0x08000f3c   0x000000b2   Code   RO         5126    .text               c_w.l(_printf_intcommon.o)
    0x08000fee   0x08000fee   0x0000041e   Code   RO         5128    .text               c_w.l(_printf_fp_dec.o)
    0x0800140c   0x0800140c   0x000002fc   Code   RO         5130    .text               c_w.l(_printf_fp_hex.o)
    0x08001708   0x08001708   0x0000002c   Code   RO         5135    .text               c_w.l(_printf_char.o)
    0x08001734   0x08001734   0x0000002c   Code   RO         5137    .text               c_w.l(_printf_wchar.o)
    0x08001760   0x08001760   0x00000040   Code   RO         5140    .text               c_w.l(_sgetc.o)
    0x080017a0   0x080017a0   0x0000009e   Code   RO         5142    .text               c_w.l(_strtoul.o)
    0x0800183e   0x0800183e   0x00000040   Code   RO         5144    .text               c_w.l(_wcrtomb.o)
    0x0800187e   0x0800187e   0x0000000a   Code   RO         5149    .text               c_w.l(defsig_exit.o)
    0x08001888   0x08001888   0x00000030   Code   RO         5151    .text               c_w.l(defsig_abrt_inner.o)
    0x080018b8   0x080018b8   0x00000020   Code   RO         5153    .text               c_w.l(strcspn.o)
    0x080018d8   0x080018d8   0x0000001c   Code   RO         5155    .text               c_w.l(strspn.o)
    0x080018f4   0x080018f4   0x0000004a   Code   RO         5167    .text               c_w.l(sys_stackheap_outer.o)
    0x0800193e   0x0800193e   0x00000002   PAD
    0x08001940   0x08001940   0x00000008   Code   RO         5172    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001948   0x08001948   0x00000080   Code   RO         5174    .text               c_w.l(_printf_fp_infnan.o)
    0x080019c8   0x080019c8   0x0000001c   Code   RO         5176    .text               c_w.l(_chval.o)
    0x080019e4   0x080019e4   0x000004f8   Code   RO         5178    .text               c_w.l(scanf_fp.o)
    0x08001edc   0x08001edc   0x000000e4   Code   RO         5180    .text               c_w.l(bigflt0.o)
    0x08001fc0   0x08001fc0   0x00000012   Code   RO         5208    .text               c_w.l(exit.o)
    0x08001fd2   0x08001fd2   0x00000032   Code   RO         5212    .text               c_w.l(defsig_general.o)
    0x08002004   0x08002004   0x0000000e   Code   RO         5273    .text               c_w.l(sys_wrch.o)
    0x08002012   0x08002012   0x00000002   PAD
    0x08002014   0x08002014   0x00000320   Code   RO         5310    .text               c_w.l(scanf_hexfp.o)
    0x08002334   0x08002334   0x00000134   Code   RO         5312    .text               c_w.l(scanf_infnan.o)
    0x08002468   0x08002468   0x00000026   Code   RO         5346    .text               c_w.l(llshl.o)
    0x0800248e   0x0800248e   0x0000003e   Code   RO         5183    CL$$btod_d2e        c_w.l(btod.o)
    0x080024cc   0x080024cc   0x00000046   Code   RO         5185    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002512   0x08002512   0x00000060   Code   RO         5184    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002572   0x08002572   0x00000338   Code   RO         5193    CL$$btod_div_common  c_w.l(btod.o)
    0x080028aa   0x080028aa   0x00000002   PAD
    0x080028ac   0x080028ac   0x00000084   Code   RO         5191    CL$$btod_e2d        c_w.l(btod.o)
    0x08002930   0x08002930   0x000000dc   Code   RO         5190    CL$$btod_e2e        c_w.l(btod.o)
    0x08002a0c   0x08002a0c   0x0000002a   Code   RO         5187    CL$$btod_ediv       c_w.l(btod.o)
    0x08002a36   0x08002a36   0x0000002a   Code   RO         5189    CL$$btod_edivd      c_w.l(btod.o)
    0x08002a60   0x08002a60   0x0000002a   Code   RO         5186    CL$$btod_emul       c_w.l(btod.o)
    0x08002a8a   0x08002a8a   0x0000002a   Code   RO         5188    CL$$btod_emuld      c_w.l(btod.o)
    0x08002ab4   0x08002ab4   0x00000244   Code   RO         5192    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002cf8   0x08002cf8   0x00000002   Code   RO          490    i.BusFault_Handler  stm32f4xx_it.o
    0x08002cfa   0x08002cfa   0x00000002   PAD
    0x08002cfc   0x08002cfc   0x0000000c   Code   RO          491    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x08002d08   0x08002d08   0x0000000c   Code   RO          492    i.DMA1_Stream2_IRQHandler  stm32f4xx_it.o
    0x08002d14   0x08002d14   0x0000000c   Code   RO          493    i.DMA1_Stream5_IRQHandler  stm32f4xx_it.o
    0x08002d20   0x08002d20   0x0000000c   Code   RO          494    i.DMA2_Stream1_IRQHandler  stm32f4xx_it.o
    0x08002d2c   0x08002d2c   0x0000000c   Code   RO          495    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08002d38   0x08002d38   0x00000028   Code   RO         1570    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08002d60   0x08002d60   0x00000054   Code   RO         1571    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002db4   0x08002db4   0x00000028   Code   RO         1572    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08002ddc   0x08002ddc   0x00000002   Code   RO          496    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002dde   0x08002dde   0x00000038   Code   RO         4562    i.Emm_V5_En_Control  emm_v5.o
    0x08002e16   0x08002e16   0x000001ec   Code   RO         4568    i.Emm_V5_Parse_Response  emm_v5.o
    0x08003002   0x08003002   0x00000070   Code   RO         4569    i.Emm_V5_Pos_Control  emm_v5.o
    0x08003072   0x08003072   0x00000034   Code   RO         4573    i.Emm_V5_Stop_Now   emm_v5.o
    0x080030a6   0x080030a6   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x080030aa   0x080030aa   0x00000092   Code   RO         1573    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x0800313c   0x0800313c   0x00000024   Code   RO         1574    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08003160   0x08003160   0x000001a0   Code   RO         1578    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08003300   0x08003300   0x000000d4   Code   RO         1579    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x080033d4   0x080033d4   0x0000006e   Code   RO         1583    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08003442   0x08003442   0x00000002   PAD
    0x08003444   0x08003444   0x000001f0   Code   RO         1466    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08003634   0x08003634   0x0000000a   Code   RO         1468    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x0800363e   0x0800363e   0x0000000a   Code   RO         1470    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08003648   0x08003648   0x0000000c   Code   RO         2016    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003654   0x08003654   0x00000188   Code   RO          667    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080037dc   0x080037dc   0x000000ac   Code   RO          287    i.HAL_I2C_MspInit   i2c.o
    0x08003888   0x08003888   0x00000010   Code   RO         2022    i.HAL_IncTick       stm32f4xx_hal.o
    0x08003898   0x08003898   0x00000034   Code   RO         2023    i.HAL_Init          stm32f4xx_hal.o
    0x080038cc   0x080038cc   0x00000040   Code   RO         2024    i.HAL_InitTick      stm32f4xx_hal.o
    0x0800390c   0x0800390c   0x00000030   Code   RO          632    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x0800393c   0x0800393c   0x0000001a   Code   RO         1858    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08003956   0x08003956   0x00000002   PAD
    0x08003958   0x08003958   0x00000040   Code   RO         1864    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003998   0x08003998   0x00000024   Code   RO         1865    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080039bc   0x080039bc   0x00000134   Code   RO         1112    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08003af0   0x08003af0   0x00000020   Code   RO         1119    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08003b10   0x08003b10   0x00000020   Code   RO         1120    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08003b30   0x08003b30   0x00000064   Code   RO         1121    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003b94   0x08003b94   0x0000036c   Code   RO         1124    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08003f00   0x08003f00   0x00000028   Code   RO         1869    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003f28   0x08003f28   0x00000054   Code   RO         2970    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08003f7c   0x08003f7c   0x00000090   Code   RO         2986    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x0800400c   0x0800400c   0x0000005a   Code   RO         2263    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08004066   0x08004066   0x00000002   PAD
    0x08004068   0x08004068   0x00000028   Code   RO          335    i.HAL_TIM_Base_MspInit  tim.o
    0x08004090   0x08004090   0x000000dc   Code   RO         2272    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x0800416c   0x0800416c   0x000000a4   Code   RO         2284    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08004210   0x08004210   0x000000a4   Code   RO          337    i.HAL_TIM_Encoder_MspInit  tim.o
    0x080042b4   0x080042b4   0x00000054   Code   RO          338    i.HAL_TIM_MspPostInit  tim.o
    0x08004308   0x08004308   0x000000cc   Code   RO         2335    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x080043d4   0x080043d4   0x0000005a   Code   RO         2338    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x0800442e   0x0800442e   0x00000002   Code   RO         2340    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x08004430   0x08004430   0x0000004a   Code   RO         3244    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x0800447a   0x0800447a   0x00000002   PAD
    0x0800447c   0x0800447c   0x000000dc   Code   RO          406    i.HAL_UARTEx_RxEventCallback  usart.o
    0x08004558   0x08004558   0x00000002   Code   RO         3260    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800455a   0x0800455a   0x00000002   PAD
    0x0800455c   0x0800455c   0x00000280   Code   RO         3263    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080047dc   0x080047dc   0x00000064   Code   RO         3264    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08004840   0x08004840   0x00000348   Code   RO          408    i.HAL_UART_MspInit  usart.o
    0x08004b88   0x08004b88   0x0000001c   Code   RO         3269    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08004ba4   0x08004ba4   0x00000030   Code   RO           14    i.HAL_UART_RxCpltCallback  main.o
    0x08004bd4   0x08004bd4   0x00000002   Code   RO         3271    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08004bd6   0x08004bd6   0x000000a0   Code   RO         3272    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08004c76   0x08004c76   0x00000002   Code   RO         3275    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08004c78   0x08004c78   0x00000002   Code   RO          497    i.HardFault_Handler  stm32f4xx_it.o
    0x08004c7a   0x08004c7a   0x00000002   PAD
    0x08004c7c   0x08004c7c   0x00000180   Code   RO         4328    i.LaserDrawCmd_ExecuteCommand  laser_draw_cmd.o
    0x08004dfc   0x08004dfc   0x00000028   Code   RO         4329    i.LaserDrawCmd_HandleArc  laser_draw_cmd.o
    0x08004e24   0x08004e24   0x00000058   Code   RO         4330    i.LaserDrawCmd_HandleCircle  laser_draw_cmd.o
    0x08004e7c   0x08004e7c   0x00000054   Code   RO         4331    i.LaserDrawCmd_HandleHeart  laser_draw_cmd.o
    0x08004ed0   0x08004ed0   0x00000058   Code   RO         4332    i.LaserDrawCmd_HandleLine  laser_draw_cmd.o
    0x08004f28   0x08004f28   0x00000054   Code   RO         4333    i.LaserDrawCmd_HandleMove  laser_draw_cmd.o
    0x08004f7c   0x08004f7c   0x0000007c   Code   RO         4334    i.LaserDrawCmd_HandleSpeed  laser_draw_cmd.o
    0x08004ff8   0x08004ff8   0x00000058   Code   RO         4335    i.LaserDrawCmd_HandleSquare  laser_draw_cmd.o
    0x08005050   0x08005050   0x00000054   Code   RO         4336    i.LaserDrawCmd_HandleStar  laser_draw_cmd.o
    0x080050a4   0x080050a4   0x00000038   Code   RO         4337    i.LaserDrawCmd_HandleTrackStart  laser_draw_cmd.o
    0x080050dc   0x080050dc   0x0000003c   Code   RO         4338    i.LaserDrawCmd_HandleTrackStop  laser_draw_cmd.o
    0x08005118   0x08005118   0x00000058   Code   RO         4339    i.LaserDrawCmd_HandleTriangle  laser_draw_cmd.o
    0x08005170   0x08005170   0x000000a0   Code   RO         4340    i.LaserDrawCmd_Init  laser_draw_cmd.o
    0x08005210   0x08005210   0x000000c8   Code   RO         4341    i.LaserDrawCmd_ParseCameraData  laser_draw_cmd.o
    0x080052d8   0x080052d8   0x000000ac   Code   RO         4342    i.LaserDrawCmd_ParseCommand  laser_draw_cmd.o
    0x08005384   0x08005384   0x00000064   Code   RO         4343    i.LaserDrawCmd_PixelToPhysical  laser_draw_cmd.o
    0x080053e8   0x080053e8   0x00000098   Code   RO         4344    i.LaserDrawCmd_Process  laser_draw_cmd.o
    0x08005480   0x08005480   0x000000f8   Code   RO         4345    i.LaserDrawCmd_ProcessTracking  laser_draw_cmd.o
    0x08005578   0x08005578   0x00000290   Code   RO         4346    i.LaserDrawCmd_ShowHelp  laser_draw_cmd.o
    0x08005808   0x08005808   0x0000010c   Code   RO         4347    i.LaserDrawCmd_ShowStatus  laser_draw_cmd.o
    0x08005914   0x08005914   0x000001b0   Code   RO         4348    i.LaserDrawCmd_ShowTrackStatus  laser_draw_cmd.o
    0x08005ac4   0x08005ac4   0x000000c8   Code   RO         4139    i.LaserDraw_Calibrate  laser_draw_bsp.o
    0x08005b8c   0x08005b8c   0x000000a0   Code   RO         4140    i.LaserDraw_CheckBounds  laser_draw_bsp.o
    0x08005c2c   0x08005c2c   0x00000198   Code   RO         4141    i.LaserDraw_DrawArc  laser_draw_bsp.o
    0x08005dc4   0x08005dc4   0x000000f8   Code   RO         4142    i.LaserDraw_DrawCircle  laser_draw_bsp.o
    0x08005ebc   0x08005ebc   0x000001c4   Code   RO         4143    i.LaserDraw_DrawHeart  laser_draw_bsp.o
    0x08006080   0x08006080   0x000000bc   Code   RO         4144    i.LaserDraw_DrawLine  laser_draw_bsp.o
    0x0800613c   0x0800613c   0x000000fc   Code   RO         4145    i.LaserDraw_DrawSquare  laser_draw_bsp.o
    0x08006238   0x08006238   0x00000110   Code   RO         4146    i.LaserDraw_DrawStar  laser_draw_bsp.o
    0x08006348   0x08006348   0x00000110   Code   RO         4147    i.LaserDraw_DrawTriangle  laser_draw_bsp.o
    0x08006458   0x08006458   0x00000044   Code   RO         4148    i.LaserDraw_EmergencyStop  laser_draw_bsp.o
    0x0800649c   0x0800649c   0x00000050   Code   RO         4149    i.LaserDraw_ExecuteMove  laser_draw_bsp.o
    0x080064ec   0x080064ec   0x0000000c   Code   RO         4150    i.LaserDraw_GetCurrentX  laser_draw_bsp.o
    0x080064f8   0x080064f8   0x0000000c   Code   RO         4151    i.LaserDraw_GetCurrentY  laser_draw_bsp.o
    0x08006504   0x08006504   0x0000000c   Code   RO         4152    i.LaserDraw_GetState  laser_draw_bsp.o
    0x08006510   0x08006510   0x00000058   Code   RO         4153    i.LaserDraw_Home    laser_draw_bsp.o
    0x08006568   0x08006568   0x000000d4   Code   RO         4154    i.LaserDraw_Init    laser_draw_bsp.o
    0x0800663c   0x0800663c   0x0000000c   Code   RO         4155    i.LaserDraw_IsLaserOn  laser_draw_bsp.o
    0x08006648   0x08006648   0x00000036   Code   RO         4157    i.LaserDraw_MoveTo  laser_draw_bsp.o
    0x0800667e   0x0800667e   0x00000002   PAD
    0x08006680   0x08006680   0x0000008c   Code   RO         4158    i.LaserDraw_Process  laser_draw_bsp.o
    0x0800670c   0x0800670c   0x00000054   Code   RO         4159    i.LaserDraw_SetSpeed  laser_draw_bsp.o
    0x08006760   0x08006760   0x00000034   Code   RO         4161    i.LaserDraw_StopPath  laser_draw_bsp.o
    0x08006794   0x08006794   0x000000cc   Code   RO         4162    i.LaserDraw_TestPattern  laser_draw_bsp.o
    0x08006860   0x08006860   0x0000001c   Code   RO         4163    i.Laser_Off         laser_draw_bsp.o
    0x0800687c   0x0800687c   0x0000001c   Code   RO         4164    i.Laser_On          laser_draw_bsp.o
    0x08006898   0x08006898   0x0000007c   Code   RO          262    i.MX_DMA_Init       dma.o
    0x08006914   0x08006914   0x00000120   Code   RO          238    i.MX_GPIO_Init      gpio.o
    0x08006a34   0x08006a34   0x00000040   Code   RO          288    i.MX_I2C1_Init      i2c.o
    0x08006a74   0x08006a74   0x00000040   Code   RO          289    i.MX_I2C2_Init      i2c.o
    0x08006ab4   0x08006ab4   0x000000d8   Code   RO          339    i.MX_TIM1_Init      tim.o
    0x08006b8c   0x08006b8c   0x0000006c   Code   RO          340    i.MX_TIM3_Init      tim.o
    0x08006bf8   0x08006bf8   0x0000006c   Code   RO          341    i.MX_TIM4_Init      tim.o
    0x08006c64   0x08006c64   0x00000050   Code   RO          409    i.MX_UART4_Init     usart.o
    0x08006cb4   0x08006cb4   0x00000050   Code   RO          410    i.MX_UART5_Init     usart.o
    0x08006d04   0x08006d04   0x00000038   Code   RO          411    i.MX_USART1_UART_Init  usart.o
    0x08006d3c   0x08006d3c   0x00000050   Code   RO          412    i.MX_USART2_UART_Init  usart.o
    0x08006d8c   0x08006d8c   0x00000038   Code   RO          413    i.MX_USART3_UART_Init  usart.o
    0x08006dc4   0x08006dc4   0x00000050   Code   RO          414    i.MX_USART6_UART_Init  usart.o
    0x08006e14   0x08006e14   0x00000002   Code   RO          498    i.MemManage_Handler  stm32f4xx_it.o
    0x08006e16   0x08006e16   0x00000002   Code   RO          499    i.NMI_Handler       stm32f4xx_it.o
    0x08006e18   0x08006e18   0x000000d8   Code   RO         4667    i.PID_INIT          mypid.o
    0x08006ef0   0x08006ef0   0x00000044   Code   RO         4668    i.PID_struct_init   mypid.o
    0x08006f34   0x08006f34   0x00000002   Code   RO          500    i.PendSV_Handler    stm32f4xx_it.o
    0x08006f36   0x08006f36   0x00000002   Code   RO          501    i.SVC_Handler       stm32f4xx_it.o
    0x08006f38   0x08006f38   0x0000002c   Code   RO         4069    i.Step_Motor_Init   step_motor_bsp.o
    0x08006f64   0x08006f64   0x0000015c   Code   RO         4070    i.Step_Motor_Move_Distance_mm  step_motor_bsp.o
    0x080070c0   0x080070c0   0x00000024   Code   RO         4075    i.Step_Motor_Stop   step_motor_bsp.o
    0x080070e4   0x080070e4   0x00000004   Code   RO          502    i.SysTick_Handler   stm32f4xx_it.o
    0x080070e8   0x080070e8   0x00000090   Code   RO           15    i.SystemClock_Config  main.o
    0x08007178   0x08007178   0x00000010   Code   RO         3598    i.SystemInit        system_stm32f4xx.o
    0x08007188   0x08007188   0x000000d0   Code   RO         2356    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08007258   0x08007258   0x00000014   Code   RO         2367    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x0800726c   0x0800726c   0x00000010   Code   RO         2368    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x0800727c   0x0800727c   0x00000060   Code   RO         2369    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x080072dc   0x080072dc   0x0000006c   Code   RO         2370    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x08007348   0x08007348   0x00000068   Code   RO         2371    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x080073b0   0x080073b0   0x00000050   Code   RO         2372    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08007400   0x08007400   0x00000022   Code   RO         2374    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08007422   0x08007422   0x00000024   Code   RO         2376    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x08007446   0x08007446   0x00000002   PAD
    0x08007448   0x08007448   0x0000002c   Code   RO          503    i.UART4_IRQHandler  stm32f4xx_it.o
    0x08007474   0x08007474   0x0000002c   Code   RO          504    i.UART5_IRQHandler  stm32f4xx_it.o
    0x080074a0   0x080074a0   0x0000000e   Code   RO         3277    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080074ae   0x080074ae   0x0000004a   Code   RO         3278    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x080074f8   0x080074f8   0x00000086   Code   RO         3279    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x0800757e   0x0800757e   0x0000001e   Code   RO         3281    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x0800759c   0x0800759c   0x0000004e   Code   RO         3287    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x080075ea   0x080075ea   0x0000001c   Code   RO         3288    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08007606   0x08007606   0x000000c2   Code   RO         3289    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080076c8   0x080076c8   0x0000010c   Code   RO         3290    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x080077d4   0x080077d4   0x000000a0   Code   RO         3291    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08007874   0x08007874   0x00000036   Code   RO         3292    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x080078aa   0x080078aa   0x00000072   Code   RO         3293    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x0800791c   0x0800791c   0x0000000c   Code   RO          505    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08007928   0x08007928   0x0000002c   Code   RO          506    i.USART2_IRQHandler  stm32f4xx_it.o
    0x08007954   0x08007954   0x0000000c   Code   RO          507    i.USART3_IRQHandler  stm32f4xx_it.o
    0x08007960   0x08007960   0x0000002c   Code   RO          508    i.USART6_IRQHandler  stm32f4xx_it.o
    0x0800798c   0x0800798c   0x00000002   Code   RO          509    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800798e   0x0800798e   0x00000030   Code   RO         5271    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080079be   0x080079be   0x00000026   Code   RO         5076    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x080079e4   0x080079e4   0x00000020   Code   RO         1871    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08007a04   0x08007a04   0x00000004   PAD
    0x08007a08   0x08007a08   0x000000f8   Code   RO         5295    i.__hardfp___mathlib_tofloat  m_wm.l(narrow.o)
    0x08007b00   0x08007b00   0x00000038   Code   RO         4868    i.__hardfp_atof     m_wm.l(atof.o)
    0x08007b38   0x08007b38   0x00000150   Code   RO         4874    i.__hardfp_cosf     m_wm.l(cosf.o)
    0x08007c88   0x08007c88   0x00000048   Code   RO         4910    i.__hardfp_fminf    m_wm.l(fminf.o)
    0x08007cd0   0x08007cd0   0x000000d0   Code   RO         5359    i.__hardfp_ldexp    m_wm.l(ldexp.o)
    0x08007da0   0x08007da0   0x00000190   Code   RO         4898    i.__hardfp_sinf     m_wm.l(sinf.o)
    0x08007f30   0x08007f30   0x00000020   Code   RO         5066    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x08007f50   0x08007f50   0x00000020   Code   RO         5068    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08007f70   0x08007f70   0x00000006   Code   RO         5079    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08007f76   0x08007f76   0x00000002   PAD
    0x08007f78   0x08007f78   0x00000010   Code   RO         5081    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08007f88   0x08007f88   0x00000010   Code   RO         5084    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08007f98   0x08007f98   0x00000012   Code   RO         5296    i.__mathlib_narrow  m_wm.l(narrow.o)
    0x08007faa   0x08007faa   0x00000002   PAD
    0x08007fac   0x08007fac   0x00000154   Code   RO         5092    i.__mathlib_rredf2  m_wm.l(rredf.o)
    0x08008100   0x08008100   0x00000014   Code   RO         5361    i.__support_ldexp   m_wm.l(ldexp.o)
    0x08008114   0x08008114   0x0000000e   Code   RO         4988    i._is_digit         c_w.l(__printf_wp.o)
    0x08008122   0x08008122   0x00000002   PAD
    0x08008124   0x08008124   0x0000002c   Code   RO         3968    i.calc_motor_angle  uart_bsp.o
    0x08008150   0x08008150   0x00000048   Code   RO         3969    i.calc_relative_angle  uart_bsp.o
    0x08008198   0x08008198   0x0000008c   Code   RO         5338    i.frexp             m_wm.l(frexp.o)
    0x08008224   0x08008224   0x000000fc   Code   RO         3918    i.key_init          key_bsp.o
    0x08008320   0x08008320   0x00000020   Code   RO         3919    i.key_proc          key_bsp.o
    0x08008340   0x08008340   0x00000040   Code   RO         3920    i.key_read          key_bsp.o
    0x08008380   0x08008380   0x000000b8   Code   RO           16    i.main              main.o
    0x08008438   0x08008438   0x00000034   Code   RO         3971    i.my_printf         uart_bsp.o
    0x0800846c   0x0800846c   0x000002f4   Code   RO         3972    i.parse_x_motor_data  uart_bsp.o
    0x08008760   0x08008760   0x000002f4   Code   RO         3973    i.parse_y_motor_data  uart_bsp.o
    0x08008a54   0x08008a54   0x0000000e   Code   RO         4675    i.pid_param_init    mypid.o
    0x08008a62   0x08008a62   0x00000002   PAD
    0x08008a64   0x08008a64   0x00000020   Code   RO         4676    i.pid_reset         mypid.o
    0x08008a84   0x08008a84   0x00000030   Code   RO         3748    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08008ab4   0x08008ab4   0x00000074   Code   RO         3749    i.rt_ringbuffer_get  ringbuffer.o
    0x08008b28   0x08008b28   0x00000030   Code   RO         3751    i.rt_ringbuffer_init  ringbuffer.o
    0x08008b58   0x08008b58   0x00000078   Code   RO         3753    i.rt_ringbuffer_put  ringbuffer.o
    0x08008bd0   0x08008bd0   0x00000020   Code   RO         3758    i.rt_ringbuffer_status  ringbuffer.o
    0x08008bf0   0x08008bf0   0x0000000c   Code   RO         3833    i.schedule_init     schedule.o
    0x08008bfc   0x08008bfc   0x0000003c   Code   RO         3834    i.schedule_run      schedule.o
    0x08008c38   0x08008c38   0x0000008c   Code   RO         3976    i.uart1_process_command  uart_bsp.o
    0x08008cc4   0x08008cc4   0x00000198   Code   RO         3977    i.uart_proc         uart_bsp.o
    0x08008e5c   0x08008e5c   0x0000002c   Code   RO         5147    locale$$code        c_w.l(lc_numeric_c.o)
    0x08008e88   0x08008e88   0x0000002c   Code   RO         5206    locale$$code        c_w.l(lc_ctype_c.o)
    0x08008eb4   0x08008eb4   0x00000062   Code   RO         4864    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08008f16   0x08008f16   0x00000002   PAD
    0x08008f18   0x08008f18   0x00000010   Code   RO         5396    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x08008f28   0x08008f28   0x00000018   Code   RO         5355    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08008f40   0x08008f40   0x00000078   Code   RO         5332    x$fpl$deqf          fz_wm.l(deqf.o)
    0x08008fb8   0x08008fb8   0x00000078   Code   RO         5357    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08009030   0x08009030   0x00000154   Code   RO         5161    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08009184   0x08009184   0x0000009c   Code   RO         5039    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08009220   0x08009220   0x0000000c   Code   RO         5043    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x0800922c   0x0800922c   0x0000006c   Code   RO         5334    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x08009298   0x08009298   0x00000056   Code   RO         4866    x$fpl$f2d           fz_wm.l(f2d.o)
    0x080092ee   0x080092ee   0x00000002   PAD
    0x080092f0   0x080092f0   0x00000052   Code   RO         5045    x$fpl$fcmp4         fz_wm.l(fcmp4.o)
    0x08009342   0x08009342   0x00000018   Code   RO         5163    x$fpl$fcmpinf       fz_wm.l(fcmpi.o)
    0x0800935a   0x0800935a   0x0000008c   Code   RO         5049    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080093e6   0x080093e6   0x0000000a   Code   RO         5287    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080093f0   0x080093f0   0x0000000a   Code   RO         5051    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x080093fa   0x080093fa   0x00000006   Code   RO         5269    x$fpl$ieeestatus    fz_wm.l(istatus.o)
    0x08009400   0x08009400   0x00000004   Code   RO         5053    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08009404   0x08009404   0x00000004   Code   RO         5055    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08009408   0x08009408   0x00000064   Code   RO         5398    x$fpl$retnan        fz_wm.l(retnan.o)
    0x0800946c   0x0800946c   0x0000005c   Code   RO         5393    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x080094c8   0x080094c8   0x00000004   Code   RO         5165    x$fpl$scanf1        fz_wm.l(scanf1.o)
    0x080094cc   0x080094cc   0x00000008   Code   RO         5289    x$fpl$scanf2        fz_wm.l(scanf2.o)
    0x080094d4   0x080094d4   0x00000030   Code   RO         5400    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x08009504   0x08009504   0x00000000   Code   RO         5061    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08009504   0x08009504   0x00000008   Data   RO         1585    .constdata          stm32f4xx_hal_dma.o
    0x0800950c   0x0800950c   0x00000010   Data   RO         3599    .constdata          system_stm32f4xx.o
    0x0800951c   0x0800951c   0x00000008   Data   RO         3600    .constdata          system_stm32f4xx.o
    0x08009524   0x08009524   0x00000aa4   Data   RO         4350    .constdata          laser_draw_cmd.o
    0x08009fc8   0x08009fc8   0x00000008   Data   RO         4947    .constdata          c_w.l(_printf_wctomb.o)
    0x08009fd0   0x08009fd0   0x00000028   Data   RO         4976    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08009ff8   0x08009ff8   0x00000011   Data   RO         4996    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800a009   0x0800a009   0x00000003   PAD
    0x0800a00c   0x0800a00c   0x00000020   Data   RO         5093    .constdata          m_wm.l(rredf.o)
    0x0800a02c   0x0800a02c   0x00000026   Data   RO         5131    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800a052   0x0800a052   0x00000002   PAD
    0x0800a054   0x0800a054   0x00000094   Data   RO         5181    .constdata          c_w.l(bigflt0.o)
    0x0800a0e8   0x0800a0e8   0x0000008d   Data   RO         4351    .conststring        laser_draw_cmd.o
    0x0800a175   0x0800a175   0x00000003   PAD
    0x0800a178   0x0800a178   0x00000020   Data   RO         5402    Region$$Table       anon$$obj.o
    0x0800a198   0x0800a198   0x00000008   Data   RO         5283    c$$dinf             fz_wm.l(fpconst.o)
    0x0800a1a0   0x0800a1a0   0x00000008   Data   RO         5286    c$$dmax             fz_wm.l(fpconst.o)
    0x0800a1a8   0x0800a1a8   0x0000001c   Data   RO         5146    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800a1c4   0x0800a1c4   0x00000110   Data   RO         5205    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800a2d4, Size: 0x00001200, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800a2d4   0x00000001   Data   RW           17    .data               main.o
    0x20000001   0x0800a2d5   0x00000003   PAD
    0x20000004   0x0800a2d8   0x0000000c   Data   RW         2030    .data               stm32f4xx_hal.o
    0x20000010   0x0800a2e4   0x00000004   Data   RW         3601    .data               system_stm32f4xx.o
    0x20000014   0x0800a2e8   0x00000034   Data   RW         3835    .data               schedule.o
    0x20000048   0x0800a31c   0x0000000c   Data   RW         3921    .data               key_bsp.o
    0x20000054   0x0800a328   0x00000030   Data   RW         3985    .data               uart_bsp.o
    0x20000084   0x0800a358   0x0000000c   Data   RW         4352    .data               laser_draw_cmd.o
    0x20000090   0x0800a364   0x00000004   Data   RW         4844    .data               c_w.l(strtok.o)
    0x20000094        -       0x000000a8   Zero   RW          290    .bss                i2c.o
    0x2000013c        -       0x000000d8   Zero   RW          342    .bss                tim.o
    0x20000214        -       0x00000490   Zero   RW          415    .bss                usart.o
    0x200006a4        -       0x00000164   Zero   RW         3978    .bss                uart_bsp.o
    0x20000808        -       0x0000000c   Zero   RW         3979    .bss                uart_bsp.o
    0x20000814        -       0x00000040   Zero   RW         3980    .bss                uart_bsp.o
    0x20000854        -       0x00000040   Zero   RW         3981    .bss                uart_bsp.o
    0x20000894        -       0x00000040   Zero   RW         3982    .bss                uart_bsp.o
    0x200008d4        -       0x00000040   Zero   RW         3983    .bss                uart_bsp.o
    0x20000914        -       0x0000001c   Zero   RW         4166    .bss                laser_draw_bsp.o
    0x20000930        -       0x0000008c   Zero   RW         4349    .bss                laser_draw_cmd.o
    0x200009bc        -       0x000001e0   Zero   RW         4678    .bss                mypid.o
    0x20000b9c        -       0x00000060   Zero   RW         5096    .bss                c_w.l(libspace.o)
    0x20000bfc   0x0800a368   0x00000004   PAD
    0x20000c00        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000e00        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800a368, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       124          4          0          0          0        858   dma.o
       712         42          0          0          0       4751   emm_v5.o
       288         20          0          0          0       1107   gpio.o
       300         46          0          0        168       2402   i2c.o
       348        200          0         12          0       1907   key_bsp.o
      3538        946          0          0         28      21225   laser_draw_bsp.o
      3656       1950       2865         12        140      17207   laser_draw_cmd.o
       380         64          0          1          0     704654   main.o
       330         44          0          0        480       4675   mypid.o
       364          0          0          0          0       6107   ringbuffer.o
        72          8          0         52          0       4258   schedule.o
        64         26        392          0       1536        852   startup_stm32f407xx.o
       428        144          0          0          0       2746   step_motor_bsp.o
       144         24          0         12          0       8829   stm32f4xx_hal.o
       198         14          0          0          0      33871   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7418   stm32f4xx_hal_dma.o
       516         46          0          0          0       2883   stm32f4xx_hal_gpio.o
       392         16          0          0          0       3243   stm32f4xx_hal_i2c.o
        48          6          0          0          0        874   stm32f4xx_hal_msp.o
      1348         76          0          0          0       5308   stm32f4xx_hal_rcc.o
      1472         80          0          0          0      12575   stm32f4xx_hal_tim.o
       228         28          0          0          0       2220   stm32f4xx_hal_tim_ex.o
      2156         28          0          0          0      16017   stm32f4xx_hal_uart.o
       280         90          0          0          0       9670   stm32f4xx_it.o
        16          4         24          4          0       1151   system_stm32f4xx.o
       720         74          0          0        216       4604   tim.o
      2228       1154          0         48        624       8370   uart_bsp.o
      1492        208          0          0       1168       6820   usart.o

    ----------------------------------------------------------------------
     22950       <USER>       <GROUP>        144       4360     896602   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        24          0          3          3          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
       158          0          0          0          0         92   _strtoul.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
        26          0          0          0          0         80   atoi.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        960   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        68          0          0          0          0         68   rt_memclr.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
        20          0          0          0          0         68   strchr.o
       128          0          0          0          0         68   strcmpv7m.o
        32          0          0          0          0         80   strcspn.o
       150          0          0          0          0         80   strncmp.o
        86          0          0          0          0         76   strncpy.o
        28          0          0          0          0         80   strspn.o
       164         14          0          0          0        120   strtod.o
        12          6          0          4          0         68   strtok.o
        68          4          0          0          0         84   strtok_int.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       120          4          0          0          0        140   deqf.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
        86          4          0          0          0        132   f2d.o
        82          4          0          0          0        160   fcmp4.o
        24          0          0          0          0        116   fcmpi.o
       140          4          0          0          0        132   fnaninf.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
         6          0          0          0          0        116   istatus.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
         4          0          0          0          0        116   scanf1.o
         8          0          0          0          0        132   scanf2.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
        56         12          0          0          0        132   atof.o
       336         56          0          0          0        136   cosf.o
        64         16          0          0          0        248   dunder.o
        72          0          0          0          0        148   fminf.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
       140         22          0          0          0        132   frexp.o
        38         12          0          0          0        348   funder.o
       228          8          0          0          0        308   ldexp.o
       266         16          0          0          0        308   narrow.o
       340         24         32          0          0        160   rredf.o
       400         56          0          0          0        212   sinf.o

    ----------------------------------------------------------------------
     14806        <USER>        <GROUP>          4        100      12140   Library Totals
        32          2          5          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

     11136        382        551          4         96       6864   c_w.l
      1612         44         16          0          0       2904   fz_wm.l
      2026        222         32          0          0       2372   m_wm.l

    ----------------------------------------------------------------------
     14806        <USER>        <GROUP>          4        100      12140   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     37756       6008       3928        148       4460     884042   Grand Totals
     37756       6008       3928        148       4460     884042   ELF Image Totals
     37756       6008       3928        148          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                41684 (  40.71kB)
    Total RW  Size (RW Data + ZI Data)              4608 (   4.50kB)
    Total ROM Size (Code + RO Data + RW Data)      41832 (  40.85kB)

==============================================================================

