#ifndef __PI_BSP_H__
#define __PI_BSP_H__

#include "bsp_system.h"

// 激光类型标识符
#define RED_LASER_ID 'R'
#define GREEN_LASER_ID 'G'

// 激光坐标数据结构
typedef struct {
    char type;    // 激光类型: 'R'表示红色激光，'G'表示绿色激光
    int x;        // X坐标
    int y;        // Y坐标
    uint8_t isValid; // 新增：指示当前数据是否有效/已更新
} LaserCoord_t;

// 矩形坐标数据结构
typedef struct {
    int x[4];  // 四个角点的X坐标
    int y[4];  // 四个角点的Y坐标
    uint8_t isValid;  // 数据是否有效
} RectCoord_t;

// 四点控制数据结构
typedef struct {
    int index;    // 当前目标点索引
    int x;        // 目标点X坐标
    int y;        // 目标点Y坐标
    uint8_t isValid;  // 数据是否有效
} FourPointCoord_t;

// 直线坐标数据结构
typedef struct {
    int start_x;      // 起始点X坐标
    int start_y;      // 起始点Y坐标
    int end_x;        // 终点X坐标
    int end_y;        // 终点Y坐标
    int segments;     // 分段数量
    uint8_t isValid;  // 数据是否有效
} LineCoord_t;

int pi_parse_data(char *buffer);
void pi_proc(void);

extern LaserCoord_t latest_red_laser_coord;
extern LaserCoord_t latest_green_laser_coord;
extern RectCoord_t latest_rect_coord;
extern FourPointCoord_t latest_four_point_coord;
extern LineCoord_t latest_line_coord;

#endif
