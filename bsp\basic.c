//#include "basic.h"
//#include "uart_bsp.h"
//#include "step_motor_bsp.h"  // 添加电机控制头文件
//#include <string.h>          // 添加memset函数
//#include <math.h>            // 添加sqrtf函数
//#include <limits.h>          // 添加INT_MAX常量

//// 全局变量定义
//BasicControlMode_t g_basic_control_mode = BASIC_MODE_TRACKING;
//int g_target_red_x = -1;  // 初始化为无效值，等待矩形检测
//int g_target_red_y = -1;  // 初始化为无效值，等待矩形检测

//// 私有变量
//static ResetStatus_t reset_status = RESET_STATUS_IDLE;
//static uint32_t reset_start_time = 0;
//static uint32_t last_check_time = 0;

///**
// * @brief 初始化basic模块
// */
//void basic_init(void)
//{
//    g_basic_control_mode = BASIC_MODE_RESET;  // 默认为复位模式

//    // 电机位置复位模式 - 不需要视觉原点
//    g_target_red_x = 0;
//    g_target_red_y = 0;
//    my_printf(&huart1, "Basic module initialized. Default mode: RESET\r\n");
//    my_printf(&huart1, "Available functions: Motor reset (PE0), Smart auto tracking (PE1)\r\n");

//    reset_status = RESET_STATUS_IDLE;
//    reset_start_time = 0;
//    last_check_time = 0;
//}

///**
// * @brief 设置控制模式 - 专注复位功能
// * @param mode 控制模式
// */
//void basic_set_mode(BasicControlMode_t mode)
//{
//    if (mode != g_basic_control_mode) {
//        g_basic_control_mode = mode;

//        switch (mode) {
//            case BASIC_MODE_TRACKING:
//                my_printf(&huart1, "Mode: TRACKING activated\r\n");
//                break;
//            case BASIC_MODE_RESET:
//                my_printf(&huart1, "Mode: RESET activated\r\n");
//                break;
//            case BASIC_MODE_SCREEN_EDGE:
//                my_printf(&huart1, "Mode: SCREEN_EDGE (Four-Point Tracking) - DISABLED to prevent conflicts\r\n");
//                break;
//            case BASIC_MODE_A4_TAPE:
//                my_printf(&huart1, "Mode: A4_TAPE activated\r\n");
//                break;
//            case BASIC_MODE_ROTATED_A4:
//                my_printf(&huart1, "Mode: ROTATED_A4 activated\r\n");
//                break;
//            case BASIC_MODE_SEGMENTED_LINE_TRACKING:
//                my_printf(&huart1, "Mode: SEGMENTED_LINE_TRACKING activated\r\n");
//                break;
//            case BASIC_MODE_LASER_AUTO_MOVE:
//                my_printf(&huart1, "Mode: LASER_AUTO_MOVE activated\r\n");
//                break;
//            default:
//                my_printf(&huart1, "Mode: UNKNOWN (%d) - Only TRACKING and RESET supported\r\n", mode);
//                break;
//        }
//    }
//}

///**
// * @brief 获取当前控制模式
// * @return 当前控制模式
// */
//BasicControlMode_t basic_get_mode(void)
//{
//    return g_basic_control_mode;
//}

//// 矩形中心计算函数已移除 - 电机位置复位不需要视觉反馈

///**
// * @brief 启动复位功能
// * @return 0:成功启动, -1:失败
// */
//int basic_start_reset(void)
//{
//    // 检查初始位置是否已保存
//    if (!initial_position_saved) {
//        my_printf(&huart1, "Reset failed: Initial motor position not saved\r\n");
//        my_printf(&huart1, "Please wait for system initialization to complete\r\n");
//        return -1;
//    }

//    my_printf(&huart1, "Starting motor position reset to initial position...\r\n");

//    // 调用电机位置复位功能
//    process_reset_command();

//    // 设置复位状态
//    reset_status = RESET_STATUS_MOVING;
//    reset_start_time = HAL_GetTick();
//    last_check_time = reset_start_time;

//    // 切换到复位模式
//    basic_set_mode(BASIC_MODE_RESET);

//    my_printf(&huart1, "Motor reset command sent. Estimated completion time: 5 seconds\r\n");

//    return 0;
//}

///**
// * @brief 获取复位状态
// * @return 复位状态
// */
//ResetStatus_t basic_get_reset_status(void)
//{
//    return reset_status;
//}

///**
// * @brief 检查复位是否完成
// * @return 1:完成, 0:未完成
// */
//int basic_is_reset_completed(void)
//{
//    return (reset_status == RESET_STATUS_COMPLETED);
//}

//// 自动循迹全局状态
//AutoTrackingState_t auto_tracking_state = {0};

//// 分段直线循迹全局状态
//SegmentedLineState_t segmented_line_state = {0};

//// 激光点自动移动全局状态
//LaserAutoMoveState_t laser_auto_move_state = {0};

//// 函数前向声明
//static void basic_update_auto_tracking_status(void);
//static void basic_update_segmented_line_status(void);
//static void calculate_segment_points(void);
//static void basic_update_laser_auto_move_status(void);

///**
// * @brief 计算两点之间的平方距离（避免开方运算）
// * @param x1, y1 第一个点的坐标
// * @param x2, y2 第二个点的坐标
// * @return 平方距离
// */
//static int calculate_distance_squared(int x1, int y1, int x2, int y2)
//{
//    int dx = x2 - x1;
//    int dy = y2 - y1;
//    return dx * dx + dy * dy;
//}

///**
// * @brief 计算两点之间的距离
// * @param x1, y1 第一个点的坐标
// * @param x2, y2 第二个点的坐标
// * @return 距离（像素）
// */
//float basic_calculate_distance(int x1, int y1, int x2, int y2)
//{
//    int dx = x2 - x1;
//    int dy = y2 - y1;
//    return sqrtf((float)(dx * dx + dy * dy));
//}

///**
// * @brief 对角点进行排序（顺时针：左上[0]→右上[1]→右下[2]→左下[3]）
// * @param corners 角点数组 [4][2]
// * @note 排序后索引0为左上角，作为循迹起始点
// */
//static void sort_corners_clockwise(int corners[4][2])
//{
//    // 计算中心点
//    int center_x = (corners[0][0] + corners[1][0] + corners[2][0] + corners[3][0]) / 4;
//    int center_y = (corners[0][1] + corners[1][1] + corners[2][1] + corners[3][1]) / 4;

//    // 使用基于角度的排序方法，确保左上角在索引0
//    typedef struct {
//        int x, y;
//        float angle;
//        int original_index;
//    } CornerWithAngle;

//    CornerWithAngle corners_with_angle[4];

//    // 计算每个角点相对于中心的角度
//    for (int i = 0; i < 4; i++) {
//        corners_with_angle[i].x = corners[i][0];
//        corners_with_angle[i].y = corners[i][1];
//        corners_with_angle[i].original_index = i;

//        // 计算角度（使用atan2f，范围-π到π）
//        float dx = corners[i][0] - center_x;
//        float dy = corners[i][1] - center_y;
//        corners_with_angle[i].angle = atan2f(dy, dx);

//        // 调整角度：从左上角(-3π/4)开始顺时针排序
//        // 左上角约-3π/4，右上角约-π/4，右下角约π/4，左下角约3π/4
//        corners_with_angle[i].angle += 3.14159f * 3.0f / 4.0f;  // 偏移使左上角角度最小
//        if (corners_with_angle[i].angle < 0) {
//            corners_with_angle[i].angle += 2 * 3.14159f;
//        }
//        if (corners_with_angle[i].angle >= 2 * 3.14159f) {
//            corners_with_angle[i].angle -= 2 * 3.14159f;
//        }
//    }

//    // 按角度排序（冒泡排序，简单可靠）
//    for (int i = 0; i < 3; i++) {
//        for (int j = 0; j < 3 - i; j++) {
//            if (corners_with_angle[j].angle > corners_with_angle[j + 1].angle) {
//                CornerWithAngle temp = corners_with_angle[j];
//                corners_with_angle[j] = corners_with_angle[j + 1];
//                corners_with_angle[j + 1] = temp;
//            }
//        }
//    }

//    // 复制排序后的结果
//    int sorted[4][2];
//    for (int i = 0; i < 4; i++) {
//        sorted[i][0] = corners_with_angle[i].x;
//        sorted[i][1] = corners_with_angle[i].y;
//    }

//    // 复制回原数组
//    for (int i = 0; i < 4; i++) {
//        corners[i][0] = sorted[i][0];
//        corners[i][1] = sorted[i][1];
//    }

//    // 调试输出
//    my_printf(&huart1, "Corner sorting: Center(%d,%d)\r\n", center_x, center_y);
//    my_printf(&huart1, "Sorted corners (clockwise from top-left):\r\n");
//    my_printf(&huart1, "[0]Top-Left: (%d,%d)\r\n", corners[0][0], corners[0][1]);
//    my_printf(&huart1, "[1]Top-Right: (%d,%d)\r\n", corners[1][0], corners[1][1]);
//    my_printf(&huart1, "[2]Bottom-Right: (%d,%d)\r\n", corners[2][0], corners[2][1]);
//    my_printf(&huart1, "[3]Bottom-Left: (%d,%d)\r\n", corners[3][0], corners[3][1]);
//}

///**
// * @brief 找到距离激光点最近的角点（已弃用，现在固定从左上角开始）
// * @param laser_x, laser_y 激光点坐标
// * @param corners 角点数组 [4][2]
// * @return 最近角点的索引
// */
///*
//static int find_nearest_corner(int laser_x, int laser_y, int corners[4][2])
//{
//    int min_distance_sq = INT_MAX;
//    int nearest_index = 0;

//    for (int i = 0; i < 4; i++) {
//        int dist_sq = calculate_distance_squared(laser_x, laser_y, corners[i][0], corners[i][1]);
//        if (dist_sq < min_distance_sq) {
//            min_distance_sq = dist_sq;
//            nearest_index = i;
//        }
//    }

//    return nearest_index;
//}
//*/

///**
// * @brief 复位状态检查和更新
// */
//static void basic_update_reset_status(void)
//{
//    if (reset_status != RESET_STATUS_MOVING) {
//        return;
//    }

//    uint32_t current_time = HAL_GetTick();
//    uint32_t elapsed_time = current_time - reset_start_time;

//    // 定期输出进度信息
//    if (current_time - last_check_time >= 1000) { // 每秒输出一次
//        my_printf(&huart1, "Motor reset progress: %.1f seconds elapsed\r\n", elapsed_time / 1000.0f);
//        last_check_time = current_time;
//    }

//    // 检查是否完成（估算5秒完成）
//    if (elapsed_time >= 5000) {
//        reset_status = RESET_STATUS_COMPLETED;

//        my_printf(&huart1, "Motor reset completed! Duration: %lu ms\r\n", elapsed_time);

//        // 切换回追踪模式
//        basic_set_mode(BASIC_MODE_TRACKING);
//        my_printf(&huart1, "Switched back to TRACKING mode\r\n");
//    }
//}

///**
// * @brief 更新原点位置（当检测到新的矩形时调用）- 已注释，专注复位功能
// */
///*
//void basic_update_origin(void)
//{
//    if (latest_rect_coord.isValid) {
//        int new_x, new_y;
//        calculate_rect_center(latest_rect_coord.x, latest_rect_coord.y, &new_x, &new_y);

//        // 只有当位置发生显著变化时才更新（避免频繁更新）
//        float distance = basic_calculate_distance(g_target_red_x, g_target_red_y, new_x, new_y);
//        if (distance > 10.0f) { // 10像素阈值
//            g_target_red_x = new_x;
//            g_target_red_y = new_y;
//            my_printf(&huart1, "Origin updated to rectangle center: (%d, %d)\r\n",
//                      g_target_red_x, g_target_red_y);
//        }
//    }
//}
//*/

///**
// * @brief basic模块处理函数（需要在主循环中调用）- 专注复位功能
// */
//void basic_proc(void)
//{
//    // 只处理复位功能，其他功能已注释
//    switch (g_basic_control_mode) {
//        case BASIC_MODE_RESET:
//            basic_update_reset_status();
//            break;

//        case BASIC_MODE_SCREEN_EDGE:
//            // 注释掉四点循迹模式，防止与分段直线循迹冲突
//            /*
//            // 四点循迹模式 - 由MaixCam控制四点坐标发送
//            // STM32端只需要执行PID控制，不需要额外处理
//            */
//            // 四点循迹模式已禁用
//            break;

//        case BASIC_MODE_AUTO_TRACKING:
//            // 智能自动循迹模式
//            basic_update_auto_tracking_status();
//            break;

//        case BASIC_MODE_SEGMENTED_LINE_TRACKING:
//            // 分段直线循迹模式
//            basic_update_segmented_line_status();
//            break;

//        case BASIC_MODE_LASER_AUTO_MOVE:
//            // 激光点自动移动模式
//            basic_update_laser_auto_move_status();
//            break;

//        case BASIC_MODE_A4_TAPE:
//            // TODO: 实现A4胶带模式
//            break;

//        case BASIC_MODE_ROTATED_A4:
//            // TODO: 实现旋转A4模式
//            break;

//        case BASIC_MODE_TRACKING:
//        default:
//            // 追踪模式：已注释，专注复位和智能循迹功能
//            // 追踪模式下不执行任何操作
//            break;
//    }
//}

///**
// * @brief 启动智能自动循迹功能
// * @return 0:成功启动, -1:失败
// */
//int basic_start_auto_tracking(void)
//{
//    // 检查矩形坐标数据是否有效
//    if (!latest_rect_coord.isValid) {
//        my_printf(&huart1, "Auto tracking failed: Rectangle data not available\r\n");
//        my_printf(&huart1, "Please ensure rectangle is detected before starting auto tracking\r\n");
//        return -1;
//    }

//    // 检查红色激光数据是否有效
//    if (!latest_red_laser_coord.isValid) {
//        my_printf(&huart1, "Auto tracking failed: Red laser data not available\r\n");
//        my_printf(&huart1, "Please ensure red laser is detected before starting auto tracking\r\n");
//        return -1;
//    }

//    // 初始化自动循迹状态
//    memset(&auto_tracking_state, 0, sizeof(auto_tracking_state));

//    // 存储角点坐标并排序
//    for (int i = 0; i < 4; i++) {
//        auto_tracking_state.corner_points[i][0] = latest_rect_coord.x[i];
//        auto_tracking_state.corner_points[i][1] = latest_rect_coord.y[i];
//    }
//    sort_corners_clockwise(auto_tracking_state.corner_points);

//    // 固定从左上角（索引0）开始循迹
//    auto_tracking_state.start_point_index = 0;  // 左上角
//    auto_tracking_state.current_target_index = 0;
//    auto_tracking_state.visited_count = 0;
//    auto_tracking_state.status = AUTO_TRACK_MOVE_TO_START;
//    auto_tracking_state.start_time = HAL_GetTick();
//    auto_tracking_state.last_check_time = auto_tracking_state.start_time;

//    // 切换到自动循迹模式
//    basic_set_mode(BASIC_MODE_AUTO_TRACKING);

//    my_printf(&huart1, "Auto tracking started!\r\n");
//    my_printf(&huart1, "Rectangle corners (clockwise): (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
//              auto_tracking_state.corner_points[0][0], auto_tracking_state.corner_points[0][1],
//              auto_tracking_state.corner_points[1][0], auto_tracking_state.corner_points[1][1],
//              auto_tracking_state.corner_points[2][0], auto_tracking_state.corner_points[2][1],
//              auto_tracking_state.corner_points[3][0], auto_tracking_state.corner_points[3][1]);
//    my_printf(&huart1, "Starting from TOP-LEFT corner [0]: (%d,%d)\r\n",
//              auto_tracking_state.corner_points[0][0],
//              auto_tracking_state.corner_points[0][1]);
//    my_printf(&huart1, "Tracking sequence: [0]Top-Left -> [1]Top-Right -> [2]Bottom-Right -> [3]Bottom-Left\r\n");

//    return 0;
//}

///**
// * @brief 自动循迹状态更新函数
// */
//static void basic_update_auto_tracking_status(void)
//{
//    if (auto_tracking_state.status == AUTO_TRACK_IDLE) {
//        return;
//    }

//    uint32_t current_time = HAL_GetTick();

//    // 检查红色激光数据是否有效
//    if (!latest_red_laser_coord.isValid) {
//        if (current_time - auto_tracking_state.last_check_time >= 1000) {
//            my_printf(&huart1, "Auto tracking: Waiting for red laser data...\r\n");
//            auto_tracking_state.last_check_time = current_time;
//        }
//        return;
//    }

//    int current_x = latest_red_laser_coord.x;
//    int current_y = latest_red_laser_coord.y;
//    int target_x = auto_tracking_state.corner_points[auto_tracking_state.current_target_index][0];
//    int target_y = auto_tracking_state.corner_points[auto_tracking_state.current_target_index][1];

//    // 计算到目标点的距离
//    float distance = basic_calculate_distance(current_x, current_y, target_x, target_y);

//    switch (auto_tracking_state.status) {
//        case AUTO_TRACK_MOVE_TO_START:
//            // 移动到起始点
//            if (distance <= RESET_ERROR_THRESHOLD) {
//                auto_tracking_state.status = AUTO_TRACK_TRACKING;
//                auto_tracking_state.visited_count = 1;
//                my_printf(&huart1, "Reached TOP-LEFT corner [0]. Starting clockwise tracking...\r\n");

//                // 设置下一个目标点（从左上角到右上角）
//                auto_tracking_state.current_target_index = 1;  // 下一个目标：右上角
//                my_printf(&huart1, "Next target: TOP-RIGHT corner [1]\r\n");
//            }
//            break;

//        case AUTO_TRACK_TRACKING:
//            // 循迹中
//            if (distance <= RESET_ERROR_THRESHOLD) {
//                auto_tracking_state.visited_count++;

//                // 显示到达的角点名称
//                const char* corner_names[4] = {"TOP-LEFT", "TOP-RIGHT", "BOTTOM-RIGHT", "BOTTOM-LEFT"};
//                my_printf(&huart1, "Reached %s corner [%d] (%d/%d)\r\n",
//                          corner_names[auto_tracking_state.current_target_index],
//                          auto_tracking_state.current_target_index,
//                          auto_tracking_state.visited_count, 4);

//                if (auto_tracking_state.visited_count >= 4) {
//                    // 完成一圈循迹
//                    auto_tracking_state.status = AUTO_TRACK_COMPLETED;
//                    uint32_t duration = current_time - auto_tracking_state.start_time;
//                    my_printf(&huart1, "Auto tracking completed! Duration: %.1f seconds\r\n",
//                              duration / 1000.0f);
//                    my_printf(&huart1, "Completed full clockwise cycle: TOP-LEFT -> TOP-RIGHT -> BOTTOM-RIGHT -> BOTTOM-LEFT\r\n");
//                    my_printf(&huart1, "Starting automatic reset...\r\n");
//                } else {
//                    // 设置下一个目标点
//                    auto_tracking_state.current_target_index = (auto_tracking_state.current_target_index + 1) % 4;
//                    my_printf(&huart1, "Next target: %s corner [%d]\r\n",
//                              corner_names[auto_tracking_state.current_target_index],
//                              auto_tracking_state.current_target_index);
//                }
//            }
//            break;

//        case AUTO_TRACK_COMPLETED:
//            // 自动触发复位
//            auto_tracking_state.status = AUTO_TRACK_RESETTING;
//            basic_start_reset();  // 调用电机位置复位
//            break;

//        case AUTO_TRACK_RESETTING:
//            // 检查复位是否完成
//            if (basic_get_reset_status() == RESET_STATUS_COMPLETED) {
//                auto_tracking_state.status = AUTO_TRACK_IDLE;
//                my_printf(&huart1, "Auto tracking cycle completed with automatic reset!\r\n");
//                my_printf(&huart1, "System ready for next auto tracking cycle\r\n");
//                // 保持当前模式，等待下次按键启动
//            }
//            break;

//        default:
//            break;
//    }

//    // 定期输出详细调试信息
//    if (current_time - auto_tracking_state.last_check_time >= 1000) {
//        if (auto_tracking_state.status == AUTO_TRACK_MOVE_TO_START ||
//            auto_tracking_state.status == AUTO_TRACK_TRACKING) {
//            my_printf(&huart1, "=== Auto Tracking Debug ===\r\n");
//            my_printf(&huart1, "Status: %s\r\n",
//                      auto_tracking_state.status == AUTO_TRACK_MOVE_TO_START ? "MOVE_TO_START" : "TRACKING");
//            my_printf(&huart1, "Current: (%d,%d) -> Target[%d]: (%d,%d)\r\n",
//                      current_x, current_y, auto_tracking_state.current_target_index, target_x, target_y);
//            my_printf(&huart1, "Distance: %.1f px (threshold: %d px)\r\n", distance, RESET_ERROR_THRESHOLD);
//            my_printf(&huart1, "Visited: %d/4 corners\r\n", auto_tracking_state.visited_count);

//            // 显示所有角点坐标
//            my_printf(&huart1, "All corners: ");
//            for (int i = 0; i < 4; i++) {
//                my_printf(&huart1, "[%d](%d,%d) ", i,
//                          auto_tracking_state.corner_points[i][0],
//                          auto_tracking_state.corner_points[i][1]);
//            }
//            my_printf(&huart1, "\r\n");
//        }
//        auto_tracking_state.last_check_time = current_time;
//    }
//}

///**
// * @brief 获取自动循迹状态
// * @return 自动循迹状态
// */
//AutoTrackingStatus_t basic_get_auto_tracking_status(void)
//{
//    return auto_tracking_state.status;
//}

///**
// * @brief 停止自动循迹功能
// */
//void basic_stop_auto_tracking(void)
//{
//    auto_tracking_state.status = AUTO_TRACK_IDLE;
//    my_printf(&huart1, "Auto tracking stopped\r\n");
//    my_printf(&huart1, "System ready for next operation\r\n");
//}

///**
// * @brief 计算分段点坐标
// */
//static void calculate_segment_points(void)
//{
//    int start_x = segmented_line_state.start_point[0];
//    int start_y = segmented_line_state.start_point[1];
//    int end_x = segmented_line_state.end_point[0];
//    int end_y = segmented_line_state.end_point[1];
//    int segments = segmented_line_state.segment_count;

//    // 计算每段的增量
//    float dx = (float)(end_x - start_x) / (segments - 1);
//    float dy = (float)(end_y - start_y) / (segments - 1);

//    // 计算各个分段点的坐标
//    for (int i = 0; i < segments; i++) {
//        segmented_line_state.segment_points[i][0] = start_x + (int)(dx * i);
//        segmented_line_state.segment_points[i][1] = start_y + (int)(dy * i);
//    }

//    // 调试输出所有分段点
//    my_printf(&huart1, "Calculated %d segment points:\r\n", segments);
//    for (int i = 0; i < segments; i++) {
//        my_printf(&huart1, "  Point[%d]: (%d,%d)\r\n", i,
//                  segmented_line_state.segment_points[i][0],
//                  segmented_line_state.segment_points[i][1]);
//    }
//}

///**
// * @brief 启动分段直线循迹功能
// * @return 0:成功启动, -1:失败
// */
//int basic_start_segmented_line_tracking(void)
//{
//    // 检查直线坐标数据是否有效
//    if (!latest_line_coord.isValid) {
//        my_printf(&huart1, "Segmented line tracking failed: Line data not available\r\n");
//        my_printf(&huart1, "Please ensure line coordinates are provided before starting\r\n");
//        return -1;
//    }

//    // 检查红色激光数据是否有效
//    if (!latest_red_laser_coord.isValid) {
//        my_printf(&huart1, "Segmented line tracking failed: Red laser data not available\r\n");
//        my_printf(&huart1, "Please ensure red laser is detected before starting\r\n");
//        return -1;
//    }

//    // 初始化分段直线循迹状态
//    memset(&segmented_line_state, 0, sizeof(segmented_line_state));

//    // 存储起始点和终点坐标
//    segmented_line_state.start_point[0] = latest_line_coord.start_x;
//    segmented_line_state.start_point[1] = latest_line_coord.start_y;
//    segmented_line_state.end_point[0] = latest_line_coord.end_x;
//    segmented_line_state.end_point[1] = latest_line_coord.end_y;
//    segmented_line_state.segment_count = latest_line_coord.segments;

//    // 计算分段点坐标
//    calculate_segment_points();

//    // 初始化状态
//    segmented_line_state.current_target_index = 0;  // 从第一个分段点开始
//    segmented_line_state.visited_count = 0;
//    segmented_line_state.status = SEGMENTED_LINE_MOVE_TO_START;
//    segmented_line_state.start_time = HAL_GetTick();
//    segmented_line_state.last_check_time = segmented_line_state.start_time;

//    // 切换到分段直线循迹模式
//    basic_set_mode(BASIC_MODE_SEGMENTED_LINE_TRACKING);

//    my_printf(&huart1, "Segmented line tracking started!\r\n");
//    my_printf(&huart1, "Line: (%d,%d) -> (%d,%d), %d segments\r\n",
//              segmented_line_state.start_point[0], segmented_line_state.start_point[1],
//              segmented_line_state.end_point[0], segmented_line_state.end_point[1],
//              segmented_line_state.segment_count);
//    my_printf(&huart1, "Moving to first segment point: (%d,%d)\r\n",
//              segmented_line_state.segment_points[0][0], segmented_line_state.segment_points[0][1]);

//    return 0;
//}

///**
// * @brief 分段直线循迹状态更新函数
// */
//static void basic_update_segmented_line_status(void)
//{
//    if (segmented_line_state.status == SEGMENTED_LINE_IDLE) {
//        return;
//    }

//    uint32_t current_time = HAL_GetTick();

//    // 检查红色激光数据是否有效
//    if (!latest_red_laser_coord.isValid) {
//        if (current_time - segmented_line_state.last_check_time >= 1000) {
//            my_printf(&huart1, "Segmented line tracking: Waiting for red laser data...\r\n");
//            segmented_line_state.last_check_time = current_time;
//        }
//        return;
//    }

//    // 获取当前位置和目标位置
//    int current_x = latest_red_laser_coord.x;
//    int current_y = latest_red_laser_coord.y;
//    int target_x = segmented_line_state.segment_points[segmented_line_state.current_target_index][0];
//    int target_y = segmented_line_state.segment_points[segmented_line_state.current_target_index][1];

//    // 计算到目标点的距离
//    float distance = basic_calculate_distance(current_x, current_y, target_x, target_y);

//    switch (segmented_line_state.status) {
//        case SEGMENTED_LINE_MOVE_TO_START:
//            // 移动到起始点
//            if (distance <= RESET_ERROR_THRESHOLD) {
//                segmented_line_state.status = SEGMENTED_LINE_TRACKING;
//                segmented_line_state.visited_count = 1;
//                my_printf(&huart1, "Reached segment point [0]. Starting segmented line tracking...\r\n");

//                // 设置下一个目标点
//                if (segmented_line_state.segment_count > 1) {
//                    segmented_line_state.current_target_index = 1;
//                    my_printf(&huart1, "Next target: segment point [1] (%d,%d)\r\n",
//                              segmented_line_state.segment_points[1][0],
//                              segmented_line_state.segment_points[1][1]);
//                }
//            }
//            break;

//        case SEGMENTED_LINE_TRACKING:
//            // 检查是否到达当前目标点
//            if (distance <= RESET_ERROR_THRESHOLD) {
//                segmented_line_state.visited_count++;
//                my_printf(&huart1, "Reached segment point [%d] (%d,%d)\r\n",
//                          segmented_line_state.current_target_index, target_x, target_y);

//                if (segmented_line_state.visited_count >= segmented_line_state.segment_count) {
//                    // 完成所有分段点的循迹
//                    segmented_line_state.status = SEGMENTED_LINE_COMPLETED;
//                    uint32_t duration = current_time - segmented_line_state.start_time;
//                    my_printf(&huart1, "Segmented line tracking completed! Duration: %.1f seconds\r\n",
//                              duration / 1000.0f);
//                    my_printf(&huart1, "Visited all %d segment points\r\n", segmented_line_state.segment_count);
//                    my_printf(&huart1, "Starting automatic reset...\r\n");
//                } else {
//                    // 设置下一个目标点
//                    segmented_line_state.current_target_index++;
//                    my_printf(&huart1, "Next target: segment point [%d] (%d,%d)\r\n",
//                              segmented_line_state.current_target_index,
//                              segmented_line_state.segment_points[segmented_line_state.current_target_index][0],
//                              segmented_line_state.segment_points[segmented_line_state.current_target_index][1]);
//                }
//            }
//            break;

//        case SEGMENTED_LINE_COMPLETED:
//            // 自动触发复位
//            segmented_line_state.status = SEGMENTED_LINE_RESETTING;
//            basic_start_reset();  // 调用电机位置复位
//            break;

//        case SEGMENTED_LINE_RESETTING:
//            // 检查复位是否完成
//            if (basic_get_reset_status() == RESET_STATUS_COMPLETED) {
//                segmented_line_state.status = SEGMENTED_LINE_IDLE;
//                my_printf(&huart1, "Segmented line tracking cycle completed with automatic reset!\r\n");
//                my_printf(&huart1, "System ready for next segmented line tracking cycle\r\n");
//                // 保持当前模式，等待下次启动
//            }
//            break;

//        default:
//            break;
//    }

//    // 定期输出详细调试信息
//    if (current_time - segmented_line_state.last_check_time >= 1000) {
//        if (segmented_line_state.status == SEGMENTED_LINE_MOVE_TO_START ||
//            segmented_line_state.status == SEGMENTED_LINE_TRACKING) {
//            my_printf(&huart1, "=== Segmented Line Tracking Debug ===\r\n");
//            my_printf(&huart1, "Status: %s\r\n",
//                      segmented_line_state.status == SEGMENTED_LINE_MOVE_TO_START ? "MOVE_TO_START" : "TRACKING");
//            my_printf(&huart1, "Current: (%d,%d) -> Target[%d]: (%d,%d)\r\n",
//                      current_x, current_y, segmented_line_state.current_target_index, target_x, target_y);
//            my_printf(&huart1, "Distance: %.1f px (threshold: %d px)\r\n", distance, RESET_ERROR_THRESHOLD);
//            my_printf(&huart1, "Visited: %d/%d segments\r\n",
//                      segmented_line_state.visited_count, segmented_line_state.segment_count);
//        }
//        segmented_line_state.last_check_time = current_time;
//    }
//}

///**
// * @brief 获取分段直线循迹状态
// * @return 分段直线循迹状态
// */
//SegmentedLineStatus_t basic_get_segmented_line_status(void)
//{
//    return segmented_line_state.status;
//}

///**
// * @brief 停止分段直线循迹功能
// */
//void basic_stop_segmented_line_tracking(void)
//{
//    segmented_line_state.status = SEGMENTED_LINE_IDLE;
//    my_printf(&huart1, "Segmented line tracking stopped\r\n");
//    my_printf(&huart1, "System ready for next operation\r\n");
//}

///**
// * @brief 启动激光点自动移动到指定角点
// * @param corner_index 目标角点索引 (0=左上角, 1=右上角, 2=右下角, 3=左下角)
// * @return 0:成功启动, -1:失败
// */
//int basic_start_laser_auto_move_to_corner(int corner_index)
//{
//    // 检查矩形坐标数据是否有效
//    if (!latest_rect_coord.isValid) {
//        my_printf(&huart1, "Laser auto move failed: Rectangle data not available\r\n");
//        my_printf(&huart1, "Please ensure rectangle is detected before starting laser auto move\r\n");
//        return -1;
//    }

//    // 检查红色激光数据是否有效
//    if (!latest_red_laser_coord.isValid) {
//        my_printf(&huart1, "Laser auto move failed: Red laser data not available\r\n");
//        my_printf(&huart1, "Please ensure red laser is detected before starting laser auto move\r\n");
//        return -1;
//    }

//    // 检查角点索引有效性
//    if (corner_index < 0 || corner_index > 3) {
//        my_printf(&huart1, "Laser auto move failed: Invalid corner index %d (must be 0-3)\r\n", corner_index);
//        return -1;
//    }

//    // 初始化激光点自动移动状态
//    memset(&laser_auto_move_state, 0, sizeof(laser_auto_move_state));

//    // 设置目标角点坐标
//    laser_auto_move_state.target_corner[0] = latest_rect_coord.x[corner_index];
//    laser_auto_move_state.target_corner[1] = latest_rect_coord.y[corner_index];
//    laser_auto_move_state.target_corner_index = corner_index;
//    laser_auto_move_state.status = LASER_AUTO_MOVE_TO_CORNER;
//    laser_auto_move_state.start_time = HAL_GetTick();
//    laser_auto_move_state.last_check_time = laser_auto_move_state.start_time;
//    laser_auto_move_state.timeout_ms = 15000;  // 15秒超时

//    // 切换到激光点自动移动模式
//    basic_set_mode(BASIC_MODE_LASER_AUTO_MOVE);

//    const char* corner_names[] = {"LEFT-TOP", "RIGHT-TOP", "RIGHT-BOTTOM", "LEFT-BOTTOM"};
//    my_printf(&huart1, "Laser auto move started!\r\n");
//    my_printf(&huart1, "Target: %s corner [%d] at (%d,%d)\r\n",
//              corner_names[corner_index], corner_index,
//              laser_auto_move_state.target_corner[0], laser_auto_move_state.target_corner[1]);
//    my_printf(&huart1, "Current laser position: (%d,%d)\r\n",
//              latest_red_laser_coord.x, latest_red_laser_coord.y);

//    return 0;
//}

///**
// * @brief 激光点自动移动状态更新函数
// */
//static void basic_update_laser_auto_move_status(void)
//{
//    if (laser_auto_move_state.status == LASER_AUTO_MOVE_IDLE) {
//        return;
//    }

//    uint32_t current_time = HAL_GetTick();

//    // 检查超时
//    if (current_time - laser_auto_move_state.start_time >= laser_auto_move_state.timeout_ms) {
//        laser_auto_move_state.status = LASER_AUTO_MOVE_TIMEOUT;
//        my_printf(&huart1, "Laser auto move timeout!\r\n");
//        my_printf(&huart1, "Failed to reach target corner within %d seconds\r\n",
//                  laser_auto_move_state.timeout_ms / 1000);
//        return;
//    }

//    // 检查红色激光数据是否有效
//    if (!latest_red_laser_coord.isValid) {
//        if (current_time - laser_auto_move_state.last_check_time >= 1000) {
//            my_printf(&huart1, "Laser auto move: Waiting for red laser data...\r\n");
//            laser_auto_move_state.last_check_time = current_time;
//        }
//        return;
//    }

//    // 获取当前激光位置和目标位置
//    int current_x = latest_red_laser_coord.x;
//    int current_y = latest_red_laser_coord.y;
//    int target_x = laser_auto_move_state.target_corner[0];
//    int target_y = laser_auto_move_state.target_corner[1];

//    // 计算到目标角点的距离
//    float distance = basic_calculate_distance(current_x, current_y, target_x, target_y);

//    switch (laser_auto_move_state.status) {
//        case LASER_AUTO_MOVE_TO_CORNER:
//            // 检查是否到达目标角点
//            if (distance <= RESET_ERROR_THRESHOLD) {
//                laser_auto_move_state.status = LASER_AUTO_MOVE_COMPLETED;
//                uint32_t duration = current_time - laser_auto_move_state.start_time;
//                const char* corner_names[] = {"LEFT-TOP", "RIGHT-TOP", "RIGHT-BOTTOM", "LEFT-BOTTOM"};

//                my_printf(&huart1, "Laser auto move completed!\r\n");
//                my_printf(&huart1, "Successfully reached %s corner [%d] in %.1f seconds\r\n",
//                          corner_names[laser_auto_move_state.target_corner_index],
//                          laser_auto_move_state.target_corner_index,
//                          duration / 1000.0f);
//                my_printf(&huart1, "Final position: (%d,%d), Target: (%d,%d), Distance: %.1f px\r\n",
//                          current_x, current_y, target_x, target_y, distance);

//                // 移动完成后，可以触发后续操作
//                // 例如：自动启动分段循迹或其他功能
//                my_printf(&huart1, "Ready for next operation (e.g., line tracking)\r\n");
//            }
//            break;

//        case LASER_AUTO_MOVE_COMPLETED:
//        case LASER_AUTO_MOVE_TIMEOUT:
//            // 完成状态或超时状态，不需要额外处理
//            break;

//        default:
//            break;
//    }

//    // 定期输出详细调试信息
//    if (current_time - laser_auto_move_state.last_check_time >= 1000) {
//        if (laser_auto_move_state.status == LASER_AUTO_MOVE_TO_CORNER) {
//            my_printf(&huart1, "=== Laser Auto Move Debug ===\r\n");
//            my_printf(&huart1, "Current: (%d,%d) -> Target[%d]: (%d,%d)\r\n",
//                      current_x, current_y, laser_auto_move_state.target_corner_index, target_x, target_y);
//            my_printf(&huart1, "Distance: %.1f px (threshold: %d px)\r\n", distance, RESET_ERROR_THRESHOLD);
//            my_printf(&huart1, "Elapsed: %.1f seconds\r\n",
//                      (current_time - laser_auto_move_state.start_time) / 1000.0f);
//        }
//        laser_auto_move_state.last_check_time = current_time;
//    }
//}

///**
// * @brief 获取激光点自动移动状态
// * @return 激光点自动移动状态
// */
//LaserAutoMoveStatus_t basic_get_laser_auto_move_status(void)
//{
//    return laser_auto_move_state.status;
//}

///**
// * @brief 停止激光点自动移动功能
// */
//void basic_stop_laser_auto_move(void)
//{
//    laser_auto_move_state.status = LASER_AUTO_MOVE_IDLE;
//    my_printf(&huart1, "Laser auto move stopped\r\n");
//    my_printf(&huart1, "System ready for next operation\r\n");
//}
