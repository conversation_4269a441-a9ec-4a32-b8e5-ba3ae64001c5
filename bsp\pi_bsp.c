//	#include "pi_bsp.h"
//	#include "basic.h"
//	#include "step_motor_bsp.h"
//	#include "../app/mypid.h"

//	// 默认值设为无效状态，X, Y 为 0
//	LaserCoord_t latest_red_laser_coord = {RED_LASER_ID, 0, 0, 0};
//	LaserCoord_t latest_green_laser_coord = {GREEN_LASER_ID, 0, 0, 0};

//	// 新增：矩形坐标和四点控制数据的全局变量定义
//	RectCoord_t latest_rect_coord = {0};
//	FourPointCoord_t latest_four_point_coord = {0};
//	LineCoord_t latest_line_coord = {0};

//	// 扩展的MaixCam数据解析函数
//	// 支持格式：red:(x,y), gre:(x,y), rect:(x1,y1,x2,y2,x3,y3,x4,y4), line:(x1,y1,x2,y2,segments)
//	// 注意：point:(index,x,y)格式已禁用，矩形会自动转换为line数据
//	int pi_parse_data(char *buffer)
//	{
//	if (!buffer)
//		return -1; // 空指针检查

//	int parsed_count;

//	// 尝试匹配 "red:(x,y)" 格式
//	if (strncmp(buffer, "red:", 4) == 0)
//	{
//		int parsed_x, parsed_y;
//		parsed_count = sscanf(buffer, "red:(%d,%d)", &parsed_x, &parsed_y);
//		if (parsed_count != 2)
//			return -2; // 解析失败

//		// 解析成功，更新全局红色激光坐标
//		latest_red_laser_coord.x = parsed_x;
//		latest_red_laser_coord.y = parsed_y;
//		latest_red_laser_coord.isValid = 1;

//		my_printf(&huart1, "Parsed RED: X=%d, Y=%d\r\n", latest_red_laser_coord.x, latest_red_laser_coord.y);
//	}
//	// 尝试匹配 "gre:(x,y)" 格式
//	else if (strncmp(buffer, "gre:", 4) == 0)
//	{
//		int parsed_x, parsed_y;
//		parsed_count = sscanf(buffer, "gre:(%d,%d)", &parsed_x, &parsed_y);
//		if (parsed_count != 2)
//			return -2; // 解析失败

//		// 解析成功，更新全局绿色激光坐标
//		latest_green_laser_coord.x = parsed_x;
//		latest_green_laser_coord.y = parsed_y;
//		latest_green_laser_coord.isValid = 1;

//		my_printf(&huart1, "Parsed GRE: X=%d, Y=%d\r\n", latest_green_laser_coord.x, latest_green_laser_coord.y);
//	}
//	// 新增：尝试匹配 "rect:(x1,y1,x2,y2,x3,y3,x4,y4)" 格式
//	else if (strncmp(buffer, "rect:", 5) == 0)
//	{
//		my_printf(&huart1, "Attempting to parse RECT data: '%s'\r\n", buffer);
//		parsed_count = sscanf(buffer, "rect:(%d,%d,%d,%d,%d,%d,%d,%d)",
//							 &latest_rect_coord.x[0], &latest_rect_coord.y[0],
//							 &latest_rect_coord.x[1], &latest_rect_coord.y[1],
//							 &latest_rect_coord.x[2], &latest_rect_coord.y[2],
//							 &latest_rect_coord.x[3], &latest_rect_coord.y[3]);

//		my_printf(&huart1, "RECT sscanf parsed %d values\r\n", parsed_count);

//		if (parsed_count != 8) {
//			my_printf(&huart1, "RECT parse failed: expected 8 values, got %d\r\n", parsed_count);
//			return -2; // 解析失败
//		}

//		latest_rect_coord.isValid = 1;
//		my_printf(&huart1, "Parsed RECT: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
//				  latest_rect_coord.x[0], latest_rect_coord.y[0],
//				  latest_rect_coord.x[1], latest_rect_coord.y[1],
//				  latest_rect_coord.x[2], latest_rect_coord.y[2],
//				  latest_rect_coord.x[3], latest_rect_coord.y[3]);

//		// 自动更新原点位置
//	//        basic_update_origin();
//	}
//	// 注释掉四点控制数据解析，防止与分段直线循迹冲突
//	/*
//	// 新增：尝试匹配 "point:(index,x,y)" 格式
//	else if (strncmp(buffer, "point:", 6) == 0)
//	{
//		parsed_count = sscanf(buffer, "point:(%d,%d,%d)",
//							 &latest_four_point_coord.index,
//							 &latest_four_point_coord.x,
//							 &latest_four_point_coord.y);
//		if (parsed_count != 3)
//			return -2; // 解析失败

//		latest_four_point_coord.isValid = 1;
//		my_printf(&huart1, "Parsed POINT: Index=%d, X=%d, Y=%d\r\n",
//				  latest_four_point_coord.index,
//				  latest_four_point_coord.x,
//				  latest_four_point_coord.y);
//	}
//	*/
//	// 新增：尝试匹配 "line:(x1,y1,x2,y2,segments)" 格式
//	else if (strncmp(buffer, "line:", 5) == 0)
//	{
//		my_printf(&huart1, "Attempting to parse LINE data: '%s'\r\n", buffer);
//		parsed_count = sscanf(buffer, "line:(%d,%d,%d,%d,%d)",
//							 &latest_line_coord.start_x, &latest_line_coord.start_y,
//							 &latest_line_coord.end_x, &latest_line_coord.end_y,
//							 &latest_line_coord.segments);

//		my_printf(&huart1, "LINE sscanf parsed %d values\r\n", parsed_count);

//		if (parsed_count != 5) {
//			my_printf(&huart1, "LINE parse failed: expected 5 values, got %d\r\n", parsed_count);
//			return -2; // 解析失败
//		}

//		// 验证分段数量的合理性
//		if (latest_line_coord.segments < 2 || latest_line_coord.segments > 20) {
//			my_printf(&huart1, "LINE parse failed: invalid segments count %d (must be 2-20)\r\n",
//					  latest_line_coord.segments);
//			return -2; // 解析失败
//		}

//		latest_line_coord.isValid = 1;
//		my_printf(&huart1, "Parsed LINE: Start(%d,%d) End(%d,%d) Segments=%d\r\n",
//				  latest_line_coord.start_x, latest_line_coord.start_y,
//				  latest_line_coord.end_x, latest_line_coord.end_y,
//				  latest_line_coord.segments);
//	}
//	else
//	{
//		// 未知格式或无效数据
//		my_printf(&huart1, "Unknown data format: '%s'\r\n", buffer);
//		return -3; // 未知或无效格式
//	}

//	return 0; // 成功
//	}



//	void pi_proc(void)
//	{
//	float pos_out_x, pos_out_y = 0;

//	// 调用basic模块处理函数
//	basic_proc();

//	// 根据控制模式选择不同的控制逻辑
//	switch (basic_get_mode()) {
//		case BASIC_MODE_TRACKING:
//			// 追踪模式：已注释，专注复位和智能循迹功能
//			/*
//			// 追踪模式：绿色激光追踪红色激光
//			if (latest_red_laser_coord.isValid && latest_green_laser_coord.isValid) {
//				// 两个激光数据都有效，正常PID控制
//				pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
//				pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
//			} else {
//				// 激光数据无效，暂停电机控制，等待数据
//				pos_out_x = 0;
//				pos_out_y = 0;
//			}
//			*/
//			// 追踪模式下停止电机
//			pos_out_x = 0;
//			pos_out_y = 0;
//			break;

//		case BASIC_MODE_RESET:
//			// 电机位置复位模式：不需要PID控制，电机自动移动到初始位置
//			pos_out_x = 0;
//			pos_out_y = 0;
//			break;

//		case BASIC_MODE_AUTO_TRACKING:
//			// 智能自动循迹模式：使用角点坐标作为目标
//			if (latest_red_laser_coord.isValid) {
//				// 获取当前目标角点坐标
//				AutoTrackingStatus_t status = basic_get_auto_tracking_status();
//				if (status == AUTO_TRACK_MOVE_TO_START || status == AUTO_TRACK_TRACKING) {
//					// 从自动循迹状态获取目标坐标
//					int target_index = auto_tracking_state.current_target_index;
//					int target_x = auto_tracking_state.corner_points[target_index][0];
//					int target_y = auto_tracking_state.corner_points[target_index][1];

//					// 执行速度控制PID
//					pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, target_x, 0);
//					pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, target_y, 0);

//					// 调试输出PID控制信息（每2秒输出一次）
//					static uint32_t last_pid_debug_time = 0;
//					uint32_t current_time = HAL_GetTick();
//					if (current_time - last_pid_debug_time >= 2000) {
//						my_printf(&huart1, "=== Speed PID Control Debug ===\r\n");
//						my_printf(&huart1, "Current: (%d,%d), Target: (%d,%d)\r\n",
//								  latest_red_laser_coord.x, latest_red_laser_coord.y, target_x, target_y);
//						my_printf(&huart1, "Speed PID Output: X=%.2f%%, Y=%.2f%%\r\n", pos_out_x, pos_out_y);
//						my_printf(&huart1, "Motor Speed Command: X=%.2f%%, Y=%.2f%%\r\n", pos_out_x, pos_out_y);
//						last_pid_debug_time = current_time;
//					}
//				} else {
//					// 非循迹状态，停止电机
//					pos_out_x = 0;
//					pos_out_y = 0;
//				}
//			} else {
//				// 红色激光数据无效，暂停电机控制
//				pos_out_x = 0;
//				pos_out_y = 0;
//			}
//			break;

//		case BASIC_MODE_SCREEN_EDGE:
//			// 注释掉四点循迹模式，防止与分段直线循迹冲突
//			/*
//			// 四点循迹模式：红色激光移动到四点目标位置
//			if (latest_red_laser_coord.isValid && latest_four_point_coord.isValid) {
//				// 红色激光数据和四点坐标都有效，执行PID控制
//				pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, latest_four_point_coord.x, 0);
//				pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, latest_four_point_coord.y, 0);

//				// 调试输出
//				my_printf(&huart1, "Four-Point Control: Current(%d,%d) -> Target(%d,%d)\r\n",
//						  latest_red_laser_coord.x, latest_red_laser_coord.y,
//						  latest_four_point_coord.x, latest_four_point_coord.y);
//			} else {
//				// 数据无效，暂停电机控制，等待数据
//				pos_out_x = 0;
//				pos_out_y = 0;
//				if (!latest_red_laser_coord.isValid) {
//					my_printf(&huart1, "Four-Point: Waiting for red laser data...\r\n");
//				}
//				if (!latest_four_point_coord.isValid) {
//					my_printf(&huart1, "Four-Point: Waiting for target point data...\r\n");
//				}
//			}
//			*/
//			// 四点循迹模式已禁用，停止电机
//			pos_out_x = 0;
//			pos_out_y = 0;
//			my_printf(&huart1, "SCREEN_EDGE mode disabled to prevent conflicts\r\n");
//			break;

//		case BASIC_MODE_SEGMENTED_LINE_TRACKING:
//			// 分段直线循迹模式：红色激光按分段点循迹
//			if (latest_red_laser_coord.isValid) {
//				// 获取当前分段循迹状态
//				SegmentedLineStatus_t status = basic_get_segmented_line_status();
//				if (status == SEGMENTED_LINE_MOVE_TO_START || status == SEGMENTED_LINE_TRACKING) {
//					// 从分段循迹状态获取目标坐标
//					int target_index = segmented_line_state.current_target_index;
//					int target_x = segmented_line_state.segment_points[target_index][0];
//					int target_y = segmented_line_state.segment_points[target_index][1];

//					// 执行速度控制PID
//					pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, target_x, 0);
//					pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, target_y, 0);

//					// 调试输出PID控制信息（每2秒输出一次）
//					static uint32_t last_segmented_debug_time = 0;
//					uint32_t current_time = HAL_GetTick();
//					if (current_time - last_segmented_debug_time >= 2000) {
//						my_printf(&huart1, "=== Segmented Line Speed PID Control Debug ===\r\n");
//						my_printf(&huart1, "Current: (%d,%d), Target[%d]: (%d,%d)\r\n",
//								  latest_red_laser_coord.x, latest_red_laser_coord.y, target_index, target_x, target_y);
//						my_printf(&huart1, "Speed PID Output: X=%.2f%%, Y=%.2f%%\r\n", pos_out_x, pos_out_y);
//						my_printf(&huart1, "Motor Speed Command: X=%.2f%%, Y=%.2f%%\r\n", pos_out_x, pos_out_y);
//						last_segmented_debug_time = current_time;
//					}
//				} else {
//					// 非循迹状态，停止电机
//					pos_out_x = 0;
//					pos_out_y = 0;
//				}
//			} else {
//				// 红色激光数据无效，暂停电机控制
//				pos_out_x = 0;
//				pos_out_y = 0;
//			}
//			break;

//		case BASIC_MODE_LASER_AUTO_MOVE:
//			// 激光点自动移动模式：红色激光移动到指定角点
//			if (latest_red_laser_coord.isValid) {
//				// 获取激光点自动移动状态
//				LaserAutoMoveStatus_t status = basic_get_laser_auto_move_status();
//				if (status == LASER_AUTO_MOVE_TO_CORNER) {
//					// 获取目标角点坐标
//					int target_x = laser_auto_move_state.target_corner[0];
//					int target_y = laser_auto_move_state.target_corner[1];

//					// 执行速度控制PID
//					pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, target_x, 0);
//					pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, target_y, 0);

//					// 调试输出PID控制信息（每2秒输出一次）
//					static uint32_t last_laser_move_debug_time = 0;
//					uint32_t current_time = HAL_GetTick();
//					if (current_time - last_laser_move_debug_time >= 2000) {
//						my_printf(&huart1, "=== Laser Auto Move Speed PID Control Debug ===\r\n");
//						my_printf(&huart1, "Current: (%d,%d), Target Corner[%d]: (%d,%d)\r\n",
//								  latest_red_laser_coord.x, latest_red_laser_coord.y,
//								  laser_auto_move_state.target_corner_index, target_x, target_y);
//						my_printf(&huart1, "Speed PID Output: X=%.2f%%, Y=%.2f%%\r\n", pos_out_x, pos_out_y);
//						my_printf(&huart1, "Motor Speed Command: X=%.2f%%, Y=%.2f%%\r\n", pos_out_x, pos_out_y);
//						last_laser_move_debug_time = current_time;
//					}
//				} else {
//					// 非移动状态，停止电机
//					pos_out_x = 0;
//					pos_out_y = 0;
//				}
//			} else {
//				// 红色激光数据无效，暂停电机控制
//				pos_out_x = 0;
//				pos_out_y = 0;
//			}
//			break;

//		case BASIC_MODE_A4_TAPE:
//		case BASIC_MODE_ROTATED_A4:
//			// 其他目标位置控制模式：红色激光移动到目标位置
//			if (latest_red_laser_coord.isValid) {
//				// 红色激光数据有效，正常速度控制PID
//				pos_out_x = pid_calc(&pid_x, latest_red_laser_coord.x, g_target_red_x, 0);
//				pos_out_y = pid_calc(&pid_y, latest_red_laser_coord.y, g_target_red_y, 0);
//			} else {
//				// 红色激光数据无效，暂停电机控制，等待数据
//				pos_out_x = 0;
//				pos_out_y = 0;
//			}
//			break;

//		default:
//			// 默认使用追踪模式（速度控制）
//			pos_out_x = pid_calc(&pid_x, latest_green_laser_coord.x, latest_red_laser_coord.x, 0);
//			pos_out_y = pid_calc(&pid_y, latest_green_laser_coord.y, latest_red_laser_coord.y, 0);
//			break;
//	}

//	// 改用速度控制：PID输出速度控制量，电机执行速度控制
//	// 实现"速度式PID"控制策略
//	Step_Motor_Set_Speed((int8_t)pos_out_x, (int8_t)pos_out_y);
//	}

