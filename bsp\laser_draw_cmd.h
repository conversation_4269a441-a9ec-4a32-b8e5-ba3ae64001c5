#ifndef __LASER_DRAW_CMD_H__
#define __LASER_DRAW_CMD_H__

#include "bsp_system.h"
#include "laser_draw_bsp.h"

// 命令缓冲区大小
#define CMD_BUFFER_SIZE     128
#define MAX_CMD_PARAMS      10

// 命令类型枚举
typedef enum {
    CMD_UNKNOWN = 0,
    CMD_HELP,               // help - 显示帮助信息
    CMD_HOME,               // home - 回到原点
    CMD_MOVE,               // move x y - 移动到指定位置
    CMD_LASER_ON,           // laser_on - 开启激光
    CMD_LASER_OFF,          // laser_off - 关闭激光
    CMD_DRAW_LINE,          // line x1 y1 x2 y2 - 绘制直线
    CMD_DRAW_SQUARE,        // square x y size - 绘制正方形
    CMD_DRAW_CIRCLE,        // circle x y radius - 绘制圆形
    CMD_DRAW_TRIANGLE,      // triangle x y size - 绘制三角形
    CMD_DRAW_STAR,          // star x y size - 绘制五角星
    CMD_DRAW_HEART,         // heart x y size - 绘制心形
    CMD_DRAW_ARC,           // arc x y radius start_angle end_angle - 绘制弧线
    CMD_SET_SPEED,          // speed draw_speed move_speed - 设置速度
    CMD_STATUS,             // status - 查询状态
    CMD_CALIBRATE,          // calibrate - 校准系统
    CMD_TEST,               // test - 绘制测试图案
    CMD_STOP,               // stop - 停止当前操作
    CMD_EMERGENCY_STOP      // estop - 紧急停止
} CommandType_t;

// 命令结构体
typedef struct {
    CommandType_t type;
    char name[16];
    float params[MAX_CMD_PARAMS];
    uint8_t param_count;
    char description[64];
} Command_t;

// 命令解析结果
typedef struct {
    CommandType_t type;
    float params[MAX_CMD_PARAMS];
    uint8_t param_count;
    int error_code;
} ParseResult_t;

// 函数声明
void LaserDrawCmd_Init(void);
void LaserDrawCmd_Process(void);
int LaserDrawCmd_ParseCommand(const char *cmd_str, ParseResult_t *result);
int LaserDrawCmd_ExecuteCommand(ParseResult_t *result);
void LaserDrawCmd_ShowHelp(void);
void LaserDrawCmd_ShowStatus(void);

// 命令处理函数
int LaserDrawCmd_HandleMove(float *params, uint8_t count);
int LaserDrawCmd_HandleLine(float *params, uint8_t count);
int LaserDrawCmd_HandleSquare(float *params, uint8_t count);
int LaserDrawCmd_HandleCircle(float *params, uint8_t count);
int LaserDrawCmd_HandleTriangle(float *params, uint8_t count);
int LaserDrawCmd_HandleStar(float *params, uint8_t count);
int LaserDrawCmd_HandleHeart(float *params, uint8_t count);
int LaserDrawCmd_HandleArc(float *params, uint8_t count);
int LaserDrawCmd_HandleSpeed(float *params, uint8_t count);

// 外部变量
extern char g_cmd_buffer[CMD_BUFFER_SIZE];
extern uint8_t g_cmd_ready;

#endif /* __LASER_DRAW_CMD_H__ */
