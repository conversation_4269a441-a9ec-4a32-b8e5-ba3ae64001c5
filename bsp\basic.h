#ifndef __BASIC_H__
#define __BASIC_H__

#include "bsp_system.h"
#include "pi_bsp.h"

// 控制模式枚举
typedef enum {
    BASIC_MODE_TRACKING = 0,    // 追踪模式（原有功能）
    BASIC_MODE_RESET,           // 复位模式
    BASIC_MODE_SCREEN_EDGE,     // 屏幕边线模式
    BASIC_MODE_A4_TAPE,         // A4胶带模式
    BASIC_MODE_ROTATED_A4,      // 旋转A4模式
    BASIC_MODE_AUTO_TRACKING,   // 智能自动循迹模式
    BASIC_MODE_SEGMENTED_LINE_TRACKING,  // 分段直线循迹模式
    BASIC_MODE_LASER_AUTO_MOVE  // 激光点自动移动模式
} BasicControlMode_t;

// 复位状态枚举
typedef enum {
    RESET_STATUS_IDLE = 0,      // 空闲状态
    RESET_STATUS_MOVING,        // 正在移动到原点
    RESET_STATUS_COMPLETED,     // 复位完成
    RESET_STATUS_TIMEOUT        // 复位超时
} ResetStatus_t;

// 自动循迹状态枚举
typedef enum {
    AUTO_TRACK_IDLE = 0,        // 等待数据
    AUTO_TRACK_FIND_NEAREST,    // 计算最近点
    AUTO_TRACK_MOVE_TO_START,   // 移动到起始点
    AUTO_TRACK_TRACKING,        // 循迹中
    AUTO_TRACK_COMPLETED,       // 循迹完成
    AUTO_TRACK_RESETTING        // 自动复位中
} AutoTrackingStatus_t;

// 自动循迹状态结构
typedef struct {
    int corner_points[4][2];    // 存储四个角点坐标 [index][x,y]
    int current_target_index;   // 当前目标点索引
    int start_point_index;      // 起始点索引
    int visited_count;          // 已访问点数
    AutoTrackingStatus_t status; // 当前状态
    uint32_t start_time;        // 开始时间
    uint32_t last_check_time;   // 上次检查时间
} AutoTrackingState_t;

// 分段直线循迹状态枚举
typedef enum {
    SEGMENTED_LINE_IDLE = 0,        // 空闲状态
    SEGMENTED_LINE_MOVE_TO_START,   // 移动到起始点
    SEGMENTED_LINE_TRACKING,        // 分段循迹中
    SEGMENTED_LINE_COMPLETED,       // 循迹完成
    SEGMENTED_LINE_RESETTING        // 自动复位中
} SegmentedLineStatus_t;

// 激光点自动移动状态枚举
typedef enum {
    LASER_AUTO_MOVE_IDLE = 0,       // 空闲状态
    LASER_AUTO_MOVE_TO_CORNER,      // 移动到指定角点
    LASER_AUTO_MOVE_COMPLETED,      // 移动完成
    LASER_AUTO_MOVE_TIMEOUT         // 移动超时
} LaserAutoMoveStatus_t;

// 分段直线循迹状态结构
typedef struct {
    int start_point[2];         // 起始点坐标 [x,y]
    int end_point[2];           // 终点坐标 [x,y]
    int segment_count;          // 分段数量
    int segment_points[20][2];  // 分段点坐标数组 [index][x,y] (最多20个段点)
    int current_target_index;   // 当前目标段点索引
    int visited_count;          // 已访问段点数
    SegmentedLineStatus_t status; // 当前状态
    uint32_t start_time;        // 开始时间
    uint32_t last_check_time;   // 上次检查时间
} SegmentedLineState_t;

// 激光点自动移动状态结构
typedef struct {
    int target_corner[2];       // 目标角点坐标 [x,y]
    int target_corner_index;    // 目标角点索引 (0=左上角)
    LaserAutoMoveStatus_t status; // 当前状态
    uint32_t start_time;        // 开始时间
    uint32_t last_check_time;   // 上次检查时间
    uint32_t timeout_ms;        // 超时时间
} LaserAutoMoveState_t;

// 屏幕参数定义（假设常见分辨率）
#define SCREEN_WIDTH        640     // 屏幕宽度（像素）
#define SCREEN_HEIGHT       480     // 屏幕高度（像素）
#define SCREEN_CENTER_X     320     // 屏幕中心X坐标（原点）
#define SCREEN_CENTER_Y     240     // 屏幕中心Y坐标（原点）

// 误差阈值定义（2cm对应的像素值，假设1cm约等于10像素）
#define RESET_ERROR_THRESHOLD   20  // 2cm误差对应20像素
#define RESET_TIMEOUT_MS        10000  // 复位超时时间10秒

// 全局变量声明
extern BasicControlMode_t g_basic_control_mode;
extern int g_target_red_x, g_target_red_y;
extern AutoTrackingState_t auto_tracking_state;
extern SegmentedLineState_t segmented_line_state;
extern LaserAutoMoveState_t laser_auto_move_state;

// 函数声明

/**
 * @brief 初始化basic模块
 */
void basic_init(void);

/**
 * @brief 设置控制模式
 * @param mode 控制模式
 */
void basic_set_mode(BasicControlMode_t mode);

/**
 * @brief 获取当前控制模式
 * @return 当前控制模式
 */
BasicControlMode_t basic_get_mode(void);

/**
 * @brief 启动复位功能
 * @return 0:成功启动, -1:失败
 */
int basic_start_reset(void);

/**
 * @brief 获取复位状态
 * @return 复位状态
 */
ResetStatus_t basic_get_reset_status(void);

/**
 * @brief 检查复位是否完成
 * @return 1:完成, 0:未完成
 */
int basic_is_reset_completed(void);

/**
 * @brief basic模块处理函数（需要在主循环中调用）
 */
void basic_proc(void);

/**
 * @brief 更新原点位置（当检测到新的矩形时调用）
 */
//void basic_update_origin(void);

/**
 * @brief 计算两点之间的距离
 * @param x1, y1 第一个点的坐标
 * @param x2, y2 第二个点的坐标
 * @return 距离（像素）
 */
float basic_calculate_distance(int x1, int y1, int x2, int y2);

/**
 * @brief 启动智能自动循迹功能
 * @return 0:成功启动, -1:失败
 */
int basic_start_auto_tracking(void);

/**
 * @brief 获取自动循迹状态
 * @return 自动循迹状态
 */
AutoTrackingStatus_t basic_get_auto_tracking_status(void);

/**
 * @brief 停止自动循迹功能
 */
void basic_stop_auto_tracking(void);

/**
 * @brief 启动分段直线循迹功能
 * @return 0:成功启动, -1:失败
 */
int basic_start_segmented_line_tracking(void);

/**
 * @brief 获取分段直线循迹状态
 * @return 分段直线循迹状态
 */
SegmentedLineStatus_t basic_get_segmented_line_status(void);

/**
 * @brief 停止分段直线循迹功能
 */
void basic_stop_segmented_line_tracking(void);

/**
 * @brief 启动激光点自动移动到指定角点
 * @param corner_index 目标角点索引 (0=左上角, 1=右上角, 2=右下角, 3=左下角)
 * @return 0:成功启动, -1:失败
 */
int basic_start_laser_auto_move_to_corner(int corner_index);

/**
 * @brief 获取激光点自动移动状态
 * @return 激光点自动移动状态
 */
LaserAutoMoveStatus_t basic_get_laser_auto_move_status(void);

/**
 * @brief 停止激光点自动移动功能
 */
void basic_stop_laser_auto_move(void);

#endif /* __BASIC_H__ */
