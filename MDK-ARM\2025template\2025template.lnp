--cpu=Cortex-M4.fp.sp
"2025template\startup_stm32f407xx.o"
"2025template\main.o"
"2025template\gpio.o"
"2025template\dma.o"
"2025template\i2c.o"
"2025template\tim.o"
"2025template\usart.o"
"2025template\stm32f4xx_it.o"
"2025template\stm32f4xx_hal_msp.o"
"2025template\stm32f4xx_hal_i2c.o"
"2025template\stm32f4xx_hal_i2c_ex.o"
"2025template\stm32f4xx_hal_rcc.o"
"2025template\stm32f4xx_hal_rcc_ex.o"
"2025template\stm32f4xx_hal_flash.o"
"2025template\stm32f4xx_hal_flash_ex.o"
"2025template\stm32f4xx_hal_flash_ramfunc.o"
"2025template\stm32f4xx_hal_gpio.o"
"2025template\stm32f4xx_hal_dma_ex.o"
"2025template\stm32f4xx_hal_dma.o"
"2025template\stm32f4xx_hal_pwr.o"
"2025template\stm32f4xx_hal_pwr_ex.o"
"2025template\stm32f4xx_hal_cortex.o"
"2025template\stm32f4xx_hal.o"
"2025template\stm32f4xx_hal_exti.o"
"2025template\stm32f4xx_hal_tim.o"
"2025template\stm32f4xx_hal_tim_ex.o"
"2025template\stm32f4xx_hal_uart.o"
"2025template\system_stm32f4xx.o"
"2025template\oled.o"
"2025template\ringbuffer.o"
"2025template\schedule.o"
"2025template\oled_bsp.o"
"2025template\key_bsp.o"
"2025template\uart_bsp.o"
"2025template\step_motor_bsp.o"
"2025template\pi_bsp.o"
"2025template\laser_draw_bsp.o"
"2025template\laser_draw_cmd.o"
"2025template\camera_data_bsp.o"
"2025template\hardware_iic.o"
"2025template\emm_v5.o"
"2025template\mypid.o"
"2025template\motor_driver_tb6612.o"
--strict --scatter "2025template\2025template.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "2025template.map" -o 2025template\2025template.axf