#include "laser_draw_bsp.h"

// 定义PI常量(如果未定义)
#ifndef M_PI
#define M_PI 3.14159265358979323846f
#endif

// 全局绘图控制结构体
LaserDrawControl_t g_laser_draw = {0};

// 私有函数声明
static void LaserDraw_UpdatePosition(float x, float y);
static int LaserDraw_ExecuteMove(float target_x, float target_y, uint8_t laser_on);

/**
 * @brief 激光绘图系统初始化
 */
void LaserDraw_Init(void)
{
    // 初始化控制结构体
    g_laser_draw.state = DRAW_STATE_IDLE;
    g_laser_draw.current_x = 5.0f;
    g_laser_draw.current_y = 5.0f;
    g_laser_draw.draw_speed = DRAW_SPEED_DEFAULT;
    g_laser_draw.move_speed = MOVE_SPEED_DEFAULT;
    g_laser_draw.laser_state = 0;
    g_laser_draw.current_path.is_active = 0;
    
    // 初始化激光控制引脚
    HAL_GPIO_WritePin(LASER_PORT, LASER_PIN, GPIO_PIN_RESET);
    g_laser_draw.laser_state = 0;

    // 电机回到原点
   
    
    my_printf(&huart1, "Laser Draw System Initialized\r\n");
    my_printf(&huart1, "Canvas Size: %.1f x %.1f mm\r\n", CANVAS_WIDTH_MM, CANVAS_HEIGHT_MM);
    my_printf(&huart1, "Step Resolution: %.1f mm\r\n", STEP_RESOLUTION);
}

/**
 * @brief 激光绘图处理函数(需要在主循环中调用)
 */
void LaserDraw_Process(void)
{
    if (g_laser_draw.current_path.is_active && g_laser_draw.state == DRAW_STATE_DRAWING) {
        DrawPath_t *path = &g_laser_draw.current_path;
        
        if (path->current_index < path->point_count) {
            DrawPoint_t *point = &path->points[path->current_index];
            
            // 执行移动到下一个点
            if (LaserDraw_ExecuteMove(point->x, point->y, point->laser_on) == 0) {
                path->current_index++;
                
                // 检查是否完成路径
                if (path->current_index >= path->point_count) {
                    g_laser_draw.state = DRAW_STATE_COMPLETED;
                    g_laser_draw.current_path.is_active = 0;
                    HAL_GPIO_WritePin(LASER_PORT, LASER_PIN, GPIO_PIN_RESET);
                    g_laser_draw.laser_state = 0;
                    my_printf(&huart1, "Drawing path completed\r\n");
                }
            }
        }
    }
}

/**
 * @brief 激光开启
 */
void Laser_On(void)
{
    HAL_GPIO_WritePin(LASER_PORT, LASER_PIN, GPIO_PIN_SET);
    g_laser_draw.laser_state = 1;
}

/**
 * @brief 激光关闭
 */
void Laser_Off(void)
{
    HAL_GPIO_WritePin(LASER_PORT, LASER_PIN, GPIO_PIN_RESET);
    g_laser_draw.laser_state = 0;
}

/**
 * @brief 激光状态切换
 */
void Laser_Toggle(void)
{
    if (g_laser_draw.laser_state) {
        Laser_Off();
    } else {
        Laser_On();
    }
}

/**
 * @brief 移动到指定位置
 * @param x 目标X坐标(毫米)
 * @param y 目标Y坐标(毫米)
 * @param laser_on 激光状态
 * @return 0:成功, -1:失败
 */
int LaserDraw_MoveTo(float x, float y, uint8_t laser_on)
{
    // 检查边界
    if (LaserDraw_CheckBounds(x, y) != 0) {
        return -1;
    }
    
    return LaserDraw_ExecuteMove(x, y, laser_on);
}

/**
 * @brief 相对移动
 * @param dx X方向偏移(毫米)
 * @param dy Y方向偏移(毫米)
 * @param laser_on 激光状态
 * @return 0:成功, -1:失败
 */
int LaserDraw_MoveRelative(float dx, float dy, uint8_t laser_on)
{
    float target_x = g_laser_draw.current_x + dx;
    float target_y = g_laser_draw.current_y + dy;
    
    return LaserDraw_MoveTo(target_x, target_y, laser_on);
}

/**
 * @brief 设置绘图速度
 * @param draw_speed 绘图速度(百分比)
 * @param move_speed 移动速度(百分比)
 * @return 0:成功, -1:失败
 */
int LaserDraw_SetSpeed(uint8_t draw_speed, uint8_t move_speed)
{
    if (draw_speed > 100 || move_speed > 100) {
        return -1;
    }
    
    g_laser_draw.draw_speed = draw_speed;
    g_laser_draw.move_speed = move_speed;
    
    my_printf(&huart1, "Speed set: Draw=%d%%, Move=%d%%\r\n", draw_speed, move_speed);
    return 0;
}

/**
 * @brief 开始执行路径
 * @param points 路径点数组
 * @param count 点数量
 * @return 0:成功, -1:失败
 */
int LaserDraw_StartPath(DrawPoint_t *points, uint16_t count)
{
    if (points == NULL || count == 0) {
        return -1;
    }
    
    if (g_laser_draw.state == DRAW_STATE_DRAWING) {
        my_printf(&huart1, "Warning: Stopping current path\r\n");
        LaserDraw_StopPath();
    }
    
    g_laser_draw.current_path.points = points;
    g_laser_draw.current_path.point_count = count;
    g_laser_draw.current_path.current_index = 0;
    g_laser_draw.current_path.is_active = 1;
    g_laser_draw.state = DRAW_STATE_DRAWING;
    
    my_printf(&huart1, "Starting path with %d points\r\n", count);
    return 0;
}

/**
 * @brief 停止路径执行
 * @return 0:成功
 */
int LaserDraw_StopPath(void)
{
    g_laser_draw.current_path.is_active = 0;
    g_laser_draw.state = DRAW_STATE_IDLE;
    Laser_Off();
    
    my_printf(&huart1, "Path stopped\r\n");
    return 0;
}

/**
 * @brief 绘制直线
 * @param start_x 起始X坐标
 * @param start_y 起始Y坐标
 * @param end_x 结束X坐标
 * @param end_y 结束Y坐标
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawLine(float start_x, float start_y, float end_x, float end_y)
{
    // 移动到起始点(激光关闭)
    if (LaserDraw_MoveTo(start_x, start_y, 0) != 0) {
        return -1;
    }
    
    // 绘制到结束点(激光开启)
    if (LaserDraw_MoveTo(end_x, end_y, 1) != 0) {
        return -1;
    }
    
    // 关闭激光
    Laser_Off();
    
    my_printf(&huart1, "Line drawn: (%.1f,%.1f) to (%.1f,%.1f)\r\n", 
              start_x, start_y, end_x, end_y);
    return 0;
}

/**
 * @brief 绘制正方形
 * @param center_x 中心X坐标
 * @param center_y 中心Y坐标
 * @param size 边长
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawSquare(float center_x, float center_y, float size)
{
    float half_size = size / 2.0f;
    
    // 计算四个顶点
    float x1 = center_x - half_size;
    float y1 = center_y - half_size;
    float x2 = center_x + half_size;
    float y2 = center_y - half_size;
    float x3 = center_x + half_size;
    float y3 = center_y + half_size;
    float x4 = center_x - half_size;
    float y4 = center_y + half_size;
    
    // 移动到起始点
    if (LaserDraw_MoveTo(x1, y1, 0) != 0) return -1;
    
    // 绘制四条边
    if (LaserDraw_MoveTo(x2, y2, 1) != 0) return -1;
    if (LaserDraw_MoveTo(x3, y3, 1) != 0) return -1;
    if (LaserDraw_MoveTo(x4, y4, 1) != 0) return -1;
    if (LaserDraw_MoveTo(x1, y1, 1) != 0) return -1;
    
    Laser_Off();
    
    my_printf(&huart1, "Square drawn: center(%.1f,%.1f), size=%.1f\r\n", 
              center_x, center_y, size);
    return 0;
}

/**
 * @brief 回到原点
 * @return 0:成功, -1:失败
 */
int LaserDraw_Home(void)
{
    g_laser_draw.state = DRAW_STATE_MOVING;
    
    // 关闭激光
    Laser_Off();
    
    // 移动到原点
    if (LaserDraw_ExecuteMove(0.0f, 0.0f, 0) == 0) {
        g_laser_draw.state = DRAW_STATE_IDLE;
        my_printf(&huart1, "Homed to origin (0,0)\r\n");
        return 0;
    }
    
    g_laser_draw.state = DRAW_STATE_ERROR;
    return -1;
}

/**
 * @brief 检查坐标边界
 * @param x X坐标
 * @param y Y坐标
 * @return 0:在边界内, -1:超出边界
 */
int LaserDraw_CheckBounds(float x, float y)
{
    if (x < 0 || x > CANVAS_WIDTH_MM || y < 0 || y > CANVAS_HEIGHT_MM) {
        my_printf(&huart1, "Error: Position (%.1f,%.1f) out of bounds\r\n", x, y);
        return -1;
    }
    return 0;
}

/**
 * @brief 紧急停止
 * @return 0:成功
 */
int LaserDraw_EmergencyStop(void)
{
    // 停止所有电机
    Step_Motor_Stop();
    
    // 关闭激光
    Laser_Off();
    
    // 停止路径
    LaserDraw_StopPath();
    
    g_laser_draw.state = DRAW_STATE_ERROR;
    
    my_printf(&huart1, "EMERGENCY STOP ACTIVATED!\r\n");
    return 0;
}

/**
 * @brief 获取当前状态
 * @return 当前绘图状态
 */
DrawState_t LaserDraw_GetState(void)
{
    return g_laser_draw.state;
}

/**
 * @brief 获取当前X坐标
 * @return 当前X坐标(毫米)
 */
float LaserDraw_GetCurrentX(void)
{
    return g_laser_draw.current_x;
}

/**
 * @brief 获取当前Y坐标
 * @return 当前Y坐标(毫米)
 */
float LaserDraw_GetCurrentY(void)
{
    return g_laser_draw.current_y;
}

/**
 * @brief 获取激光状态
 * @return 1:开启, 0:关闭
 */
uint8_t LaserDraw_IsLaserOn(void)
{
    return g_laser_draw.laser_state;
}

// 私有函数实现

/**
 * @brief 执行电机移动
 * @param target_x 目标X坐标
 * @param target_y 目标Y坐标
 * @param laser_on 激光状态
 * @return 0:成功, -1:失败
 */
static int LaserDraw_ExecuteMove(float target_x, float target_y, uint8_t laser_on)
{
    // 计算移动距离(毫米)
    float dx = target_x - g_laser_draw.current_x;
    float dy = target_y - g_laser_draw.current_y;
    
    // 设置激光状态
    if (laser_on) {
        Laser_On();
    } else {
        Laser_Off();
    }
    
    // 选择速度
    uint8_t speed = laser_on ? g_laser_draw.draw_speed : g_laser_draw.move_speed;
    
    // 调用步进电机移动函数
    Step_Motor_Move_Distance_mm(dx, dy, speed);
    
    // 更新当前位置
    LaserDraw_UpdatePosition(target_x, target_y);
    
    return 0;
}

/**
 * @brief 更新当前位置
 * @param x 新的X坐标
 * @param y 新的Y坐标
 */
static void LaserDraw_UpdatePosition(float x, float y)
{
    g_laser_draw.current_x = x;
    g_laser_draw.current_y = y;
}

/**
 * @brief 绘制圆形
 * @param center_x 圆心X坐标
 * @param center_y 圆心Y坐标
 * @param radius 半径
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawCircle(float center_x, float center_y, float radius)
{
    const int segments = 36; // 36个线段组成圆形
    float angle_step = 2.0f * M_PI / segments;

    // 移动到起始点(激光关闭)
    float start_x = center_x + radius;
    float start_y = center_y;
    if (LaserDraw_MoveTo(start_x, start_y, 0) != 0) return -1;

    // 开启激光并绘制圆形
    Laser_On();
    for (int i = 1; i <= segments; i++) {
        float angle = i * angle_step;
        float x = center_x + radius * cosf(angle);
        float y = center_y + radius * sinf(angle);

        if (LaserDraw_MoveTo(x, y, 1) != 0) {
            Laser_Off();
            return -1;
        }
    }

    Laser_Off();
    my_printf(&huart1, "Circle drawn: center(%.1f,%.1f), radius=%.1f\r\n",
              center_x, center_y, radius);
    return 0;
}

/**
 * @brief 绘制三角形
 * @param center_x 中心X坐标
 * @param center_y 中心Y坐标
 * @param size 大小
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawTriangle(float center_x, float center_y, float size)
{
    float height = size * 0.866f; // sqrt(3)/2

    // 计算三个顶点
    float x1 = center_x;
    float y1 = center_y + height * 2.0f / 3.0f;
    float x2 = center_x - size / 2.0f;
    float y2 = center_y - height / 3.0f;
    float x3 = center_x + size / 2.0f;
    float y3 = center_y - height / 3.0f;

    // 移动到起始点
    if (LaserDraw_MoveTo(x1, y1, 0) != 0) return -1;

    // 绘制三条边
    if (LaserDraw_MoveTo(x2, y2, 1) != 0) return -1;
    if (LaserDraw_MoveTo(x3, y3, 1) != 0) return -1;
    if (LaserDraw_MoveTo(x1, y1, 1) != 0) return -1;

    Laser_Off();
    my_printf(&huart1, "Triangle drawn: center(%.1f,%.1f), size=%.1f\r\n",
              center_x, center_y, size);
    return 0;
}

/**
 * @brief 绘制五角星
 * @param center_x 中心X坐标
 * @param center_y 中心Y坐标
 * @param size 大小
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawStar(float center_x, float center_y, float size)
{
    const int points = 5;
    float outer_radius = size / 2.0f;
    float inner_radius = outer_radius * 0.4f;
    float angle_step = M_PI / points; // 36度

    // 移动到第一个外顶点
    float start_x = center_x + outer_radius;
    float start_y = center_y;
    if (LaserDraw_MoveTo(start_x, start_y, 0) != 0) return -1;

    Laser_On();

    // 绘制五角星
    for (int i = 0; i < points * 2; i++) {
        float angle = i * angle_step;
        float radius = (i % 2 == 0) ? outer_radius : inner_radius;
        float x = center_x + radius * cosf(angle);
        float y = center_y + radius * sinf(angle);

        if (LaserDraw_MoveTo(x, y, 1) != 0) {
            Laser_Off();
            return -1;
        }
    }

    Laser_Off();
    my_printf(&huart1, "Star drawn: center(%.1f,%.1f), size=%.1f\r\n",
              center_x, center_y, size);
    return 0;
}

/**
 * @brief 绘制心形
 * @param center_x 中心X坐标
 * @param center_y 中心Y坐标
 * @param size 大小
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawHeart(float center_x, float center_y, float size)
{
    const int segments = 100;
    float scale = size / 20.0f;

    // 心形参数方程: x = 16sin³(t), y = 13cos(t) - 5cos(2t) - 2cos(3t) - cos(4t)

    // 移动到起始点
    float t = 0;
    float x = center_x + scale * 16 * sinf(t) * sinf(t) * sinf(t);
    float y = center_y - scale * (13 * cosf(t) - 5 * cosf(2*t) - 2 * cosf(3*t) - cosf(4*t));

    if (LaserDraw_MoveTo(x, y, 0) != 0) return -1;

    Laser_On();

    // 绘制心形曲线
    for (int i = 1; i <= segments; i++) {
        t = 2.0f * M_PI * i / segments;
        x = center_x + scale * 16 * sinf(t) * sinf(t) * sinf(t);
        y = center_y - scale * (13 * cosf(t) - 5 * cosf(2*t) - 2 * cosf(3*t) - cosf(4*t));

        if (LaserDraw_MoveTo(x, y, 1) != 0) {
            Laser_Off();
            return -1;
        }
    }

    Laser_Off();
    my_printf(&huart1, "Heart drawn: center(%.1f,%.1f), size=%.1f\r\n",
              center_x, center_y, size);
    return 0;
}

/**
 * @brief 绘制弧线
 * @param center_x 圆心X坐标
 * @param center_y 圆心Y坐标
 * @param radius 半径
 * @param start_angle 起始角度(度)
 * @param end_angle 结束角度(度)
 * @return 0:成功, -1:失败
 */
int LaserDraw_DrawArc(float center_x, float center_y, float radius,
                      float start_angle, float end_angle)
{
    float start_rad = start_angle * M_PI / 180.0f;
    float end_rad = end_angle * M_PI / 180.0f;
    float angle_diff = end_rad - start_rad;

    int segments = (int)(fabsf(angle_diff) * 180.0f / M_PI / 5.0f); // 每5度一段
    if (segments < 2) segments = 2;

    // 移动到起始点
    float start_x = center_x + radius * cosf(start_rad);
    float start_y = center_y + radius * sinf(start_rad);
    if (LaserDraw_MoveTo(start_x, start_y, 0) != 0) return -1;

    Laser_On();

    // 绘制弧线
    for (int i = 1; i <= segments; i++) {
        float angle = start_rad + angle_diff * i / segments;
        float x = center_x + radius * cosf(angle);
        float y = center_y + radius * sinf(angle);

        if (LaserDraw_MoveTo(x, y, 1) != 0) {
            Laser_Off();
            return -1;
        }
    }

    Laser_Off();
    my_printf(&huart1, "Arc drawn: center(%.1f,%.1f), radius=%.1f, %.1f°-%.1f°\r\n",
              center_x, center_y, radius, start_angle, end_angle);
    return 0;
}

/**
 * @brief 校准系统
 * @return 0:成功, -1:失败
 */
int LaserDraw_Calibrate(void)
{
    my_printf(&huart1, "Starting calibration...\r\n");

    // 回到原点
    if (LaserDraw_Home() != 0) return -1;

    // 绘制校准图案(边框)
    if (LaserDraw_DrawSquare(CANVAS_WIDTH_MM/2, CANVAS_HEIGHT_MM/2,
                            fminf(CANVAS_WIDTH_MM, CANVAS_HEIGHT_MM) * 0.9f) != 0) {
        return -1;
    }

    // 绘制中心十字
    float center_x = CANVAS_WIDTH_MM / 2;
    float center_y = CANVAS_HEIGHT_MM / 2;
    float cross_size = 10.0f;

    LaserDraw_DrawLine(center_x - cross_size, center_y, center_x + cross_size, center_y);
    LaserDraw_DrawLine(center_x, center_y - cross_size, center_x, center_y + cross_size);

    my_printf(&huart1, "Calibration completed\r\n");
    return 0;
}

/**
 * @brief 测试图案
 * @return 0:成功, -1:失败
 */
int LaserDraw_TestPattern(void)
{
    my_printf(&huart1, "Drawing test pattern...\r\n");

    float center_x = CANVAS_WIDTH_MM / 2;
    float center_y = CANVAS_HEIGHT_MM / 2;
    float size = 20.0f;

    // 绘制各种图形
    LaserDraw_DrawSquare(center_x - 30, center_y + 20, size);
    LaserDraw_DrawCircle(center_x, center_y + 20, size/2);
    LaserDraw_DrawTriangle(center_x + 30, center_y + 20, size);
    LaserDraw_DrawStar(center_x - 30, center_y - 20, size);
    LaserDraw_DrawHeart(center_x + 30, center_y - 20, size);

    my_printf(&huart1, "Test pattern completed\r\n");
    return 0;
}
