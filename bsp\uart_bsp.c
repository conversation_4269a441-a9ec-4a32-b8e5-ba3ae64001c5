#include "uart_bsp.h"
#include "basic.h"
#include "laser_draw_cmd.h"

struct rt_ringbuffer ringbuffer_x;
struct rt_ringbuffer ringbuffer_y;
struct rt_ringbuffer ringbuffer_pi;
struct rt_ringbuffer ringbuffer_test;
	
uint8_t ringbuffer_pool_x[64];
uint8_t ringbuffer_pool_y[64];
uint8_t ringbuffer_pool_pi[64];
uint8_t ringbuffer_pool_test[64];

uint8_t output_buffer_x[64];
uint8_t output_buffer_y[64];
uint8_t output_buffer_pi[64];
uint8_t output_buffer_test[64];

static char line_buffer[128]; // 足够容纳最长的单行数据，例如 64 字节 + \n + \0
static int line_buffer_idx = 0; // 当前行缓冲区的写入位置

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	int retval;
	va_list local_argv;
	
	va_start(local_argv, format);
	retval = vsnprintf(buffer, sizeof(buffer), format, local_argv);
	va_end(local_argv);
	
	HAL_UART_Transmit(huart,(uint8_t *)buffer, retval, HAL_MAX_DELAY);
	return retval;
}

// 全局变量用于存储XY电机角度
float x_motor_angle = 0.0f;
float y_motor_angle = 0.0f;
// 电机是否超出角度范围标志
uint8_t x_angle_limit_flag = 0;
uint8_t y_angle_limit_flag = 0;

// 电机角度限幅检查标志，由主程序控制是否启用
uint8_t motor_angle_limit_check_enabled = 0;

// 参考位置变量和初始化标志
uint32_t x_reference_position = 0;
uint32_t y_reference_position = 0;
uint8_t x_reference_initialized = 0;
uint8_t y_reference_initialized = 0;
float x_relative_angle = 0.0f;
float y_relative_angle = 0.0f;

// 上电初始位置存储
uint32_t x_initial_position = 0;
uint32_t y_initial_position = 0;
uint8_t x_initial_direction = 0;
uint8_t y_initial_direction = 0;
uint8_t initial_position_saved = 0;

// 角度限幅检查函数
void check_motor_angle_limits(void)
{
    // 仅当限幅检查功能启用时执行
    if (!motor_angle_limit_check_enabled)
        return;

    // 检查X轴电机相对角度是否超出限制
    if (x_relative_angle > MOTOR_MAX_ANGLE || x_relative_angle < -MOTOR_MAX_ANGLE)
    {
        if (x_angle_limit_flag == 0)
        {
            my_printf(&huart1, "X轴电机相对角度超出限制(±%d°)，停止运动!\r\n", MOTOR_MAX_ANGLE);
            x_angle_limit_flag = 1;
            // 停止电机
            Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);
        }
    }
    else
    {
        x_angle_limit_flag = 0;
    }

    // 检查Y轴电机相对角度是否超出限制
    if (y_relative_angle > MOTOR_MAX_ANGLE || y_relative_angle < -MOTOR_MAX_ANGLE)
    {
        if (y_angle_limit_flag == 0)
        {
            my_printf(&huart1, "Y轴电机相对角度超出限制(±%d°)，停止运动!\r\n", MOTOR_MAX_ANGLE);
            y_angle_limit_flag = 1;
            // 停止电机
            Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
        }
    }
    else
    {
        y_angle_limit_flag = 0;
    }
}

// 将电机位置值转换为角度
float calc_motor_angle(uint8_t dir, uint32_t position)
{
    float angle;
    // 确保位置值在0-65535范围内
    position = position % 65536;

    // 计算角度值
    angle = ((float)position * 360.0f) / 65536.0f;

    // 如果是负方向，角度取负
    if (dir)
    {
        angle = -angle;
    }

    return angle;
}

// 计算相对角度的函数
float calc_relative_angle(uint8_t dir, uint32_t current_position, uint32_t reference_position)
{
    // 确保位置值在0-65535范围内
    current_position = current_position % 65536;
    reference_position = reference_position % 65536;

    // 计算相对位置差
    int32_t relative_position;
    if (current_position >= reference_position)
    {
        relative_position = current_position - reference_position;
    }
    else
    {
        // 处理过零点情况
        relative_position = 65536 - reference_position + current_position;
    }

    // 如果相对位置大于半圈，认为是反方向的较短距离
    if (relative_position > 32768)
    {
        relative_position = relative_position - 65536;
    }

    // 计算相对角度
    float angle = ((float)relative_position * 360.0f) / 65536.0f;

    // 如果是负方向，角度取负
    if (dir)
    {
        angle = -angle;
    }

    return angle;
}

// X轴电机数据处理函数
void parse_x_motor_data(Emm_V5_Response_t *resp)
{
    // 根据功能码处理不同类型的数据
    switch (resp->func)
    {
    case 0x35: // 读取实时转速
        //my_printf(&huart1, "X轴电机地址:%d 实时转速:%d RPM\r\n", resp->addr, resp->speed);
        break;

    case 0x36: // 读取实时位置
        // 计算X轴电机绝对角度
        x_motor_angle = calc_motor_angle(resp->dir, resp->position);

        // 初始化参考位置或计算相对角度
        if (!x_reference_initialized)
        {
            x_reference_position = resp->position;
            x_reference_initialized = 1;
            x_relative_angle = 0.0f;
            my_printf(&huart1, "X轴电机参考位置已初始化: %ld 脉冲\r\n", x_reference_position);
        }
        else
        {
            // 计算相对角度
            x_relative_angle = calc_relative_angle(resp->dir, resp->position, x_reference_position);
        }
        
        // 保存初始位置信息
        if (!initial_position_saved && y_reference_initialized)
        {
            x_initial_position = resp->position;
            x_initial_direction = resp->dir;
            initial_position_saved = 1;
            my_printf(&huart1, "初始位置已保存: X=%ld Y=%ld\r\n", x_initial_position, y_initial_position);
        }

        //my_printf(&huart1, "X轴电机地址:%d 实时位置:%ld 脉冲 绝对角度:%.2f° 相对角度:%.2f°\r\n",
        //          resp->addr, resp->position, x_motor_angle, x_relative_angle);
        break;

    case 0x1F: // 读取固件版本
        my_printf(&huart1, "X轴电机地址:%d 固件版本:%s\r\n", resp->addr, resp->version);
        break;

    case 0x24: // 读取总线电压
        my_printf(&huart1, "X轴电机地址:%d 总线电压:%d V\r\n", resp->addr, resp->voltage);
        break;

    case 0x27: // 读取相电流
        my_printf(&huart1, "X轴电机地址:%d 相电流:%d mA\r\n", resp->addr, resp->current);
        break;

    case 0x33: // 读取电机状态
        my_printf(&huart1, "X轴电机地址:%d 状态值:0x%02X\r\n", resp->addr, resp->status);
        // 根据状态值进一步解析具体状态
        if (resp->status & 0x01)
            my_printf(&huart1, "  X轴电机已使能\r\n");
        if (resp->status & 0x02)
            my_printf(&huart1, "  X轴电机已到位\r\n");
        if (resp->status & 0x04)
            my_printf(&huart1, "  X轴电机堵转保护\r\n");
        break;

    case 0x3B: // 读取回零状态
        my_printf(&huart1, "X轴电机地址:%d 回零状态:0x%02X\r\n", resp->addr, resp->origin_state);
        // 根据回零状态值进一步解析
        if (resp->origin_state == 0)
            my_printf(&huart1, "  X轴未处于回零状态\r\n");
        else if (resp->origin_state == 1)
            my_printf(&huart1, "  X轴正在回零\r\n");
        else if (resp->origin_state == 2)
            my_printf(&huart1, "  X轴回零完成\r\n");
        else if (resp->origin_state == 3)
            my_printf(&huart1, "  X轴回零失败\r\n");
        break;

    default:
        // 其他功能码，可直接访问resp中的结构体成员获取解析后的数据
       // my_printf(&huart1, "X轴电机地址:%d 功能码:0x%02X 已接收未处理\r\n", resp->addr, resp->func);
        break;
    }
}

// Y轴电机数据处理函数
void parse_y_motor_data(Emm_V5_Response_t *resp)
{
    // 根据功能码处理不同类型的数据
    switch (resp->func)
    {
    case 0x35: // 读取实时转速
        //my_printf(&huart1, "Y轴电机地址:%d 实时转速:%d RPM\r\n", resp->addr, resp->speed);
        break;

    case 0x36: // 读取实时位置
        // 计算Y轴电机绝对角度
        y_motor_angle = calc_motor_angle(resp->dir, resp->position);

        // 初始化参考位置或计算相对角度
        if (!y_reference_initialized)
        {
            y_reference_position = resp->position;
            y_reference_initialized = 1;
            y_relative_angle = 0.0f;
            my_printf(&huart1, "Y轴电机参考位置已初始化: %ld 脉冲\r\n", y_reference_position);
        }
        else
        {
            // 计算相对角度
            y_relative_angle = calc_relative_angle(resp->dir, resp->position, y_reference_position);
        }
        
        // 保存初始位置信息
        if (!initial_position_saved && x_reference_initialized)
        {
            y_initial_position = resp->position;
            y_initial_direction = resp->dir;
            initial_position_saved = 1;
            my_printf(&huart1, "初始位置已保存: X=%ld Y=%ld\r\n", x_initial_position, y_initial_position);
        }

        //my_printf(&huart1, "Y轴电机地址:%d 实时位置:%ld 脉冲 绝对角度:%.2f° 相对角度:%.2f°\r\n",
        //          resp->addr, resp->position, y_motor_angle, y_relative_angle);
        break;

    case 0x1F: // 读取固件版本
        my_printf(&huart1, "Y轴电机地址:%d 固件版本:%s\r\n", resp->addr, resp->version);
        break;

    case 0x24: // 读取总线电压
        my_printf(&huart1, "Y轴电机地址:%d 总线电压:%d V\r\n", resp->addr, resp->voltage);
        break;

    case 0x27: // 读取相电流
        my_printf(&huart1, "Y轴电机地址:%d 相电流:%d mA\r\n", resp->addr, resp->current);
        break;

    case 0x33: // 读取电机状态
        my_printf(&huart1, "Y轴电机地址:%d 状态值:0x%02X\r\n", resp->addr, resp->status);
        // 根据状态值进一步解析具体状态
        if (resp->status & 0x01)
            my_printf(&huart1, "  Y轴电机已使能\r\n");
        if (resp->status & 0x02)
            my_printf(&huart1, "  Y轴电机已到位\r\n");
        if (resp->status & 0x04)
            my_printf(&huart1, "  Y轴电机堵转保护\r\n");
        break;

    case 0x3B: // 读取回零状态
        my_printf(&huart1, "Y轴电机地址:%d 回零状态:0x%02X\r\n", resp->addr, resp->origin_state);
        // 根据回零状态值进一步解析
        if (resp->origin_state == 0)
            my_printf(&huart1, "  Y轴未处于回零状态\r\n");
        else if (resp->origin_state == 1)
            my_printf(&huart1, "  Y轴正在回零\r\n");
        else if (resp->origin_state == 2)
            my_printf(&huart1, "  Y轴回零完成\r\n");
        else if (resp->origin_state == 3)
            my_printf(&huart1, "  Y轴回零失败\r\n");
        break;

    default:
        // 其他功能码，可直接访问resp中的结构体成员获取解析后的数据
        //my_printf(&huart1, "Y轴电机地址:%d 功能码:0x%02X 已接收未处理\r\n", resp->addr, resp->func);
        break;
    }
}



// 处理复位指令，让电机回到初始位置
void process_reset_command(void)
{
    // 只有当初始位置已保存时才执行
    if (initial_position_saved)
    {
        my_printf(&huart1, "正在复位电机到初始位置...\r\n");
        // 使用绝对位置模式回到初始位置
        // X轴电机
        Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_initial_direction, 
                           MOTOR_MAX_SPEED/2, MOTOR_ACCEL, x_initial_position, 
                           true, MOTOR_SYNC_FLAG);
        
        // Y轴电机
        Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_initial_direction, 
                           MOTOR_MAX_SPEED/2, MOTOR_ACCEL, y_initial_position, 
                           true, MOTOR_SYNC_FLAG);
    }
    else
    {
        my_printf(&huart1, "错误：未保存初始位置，无法复位\r\n");
    }
}

// 保存初始位置信息
void save_initial_position(void)
{
    // 上电时读取当前位置并保存为初始位置
    if (!initial_position_saved)
    {
        // 读取X轴位置
        Emm_V5_Read_Sys_Params(&MOTOR_X_UART, MOTOR_X_ADDR, S_CPOS);
        // 读取Y轴位置
        Emm_V5_Read_Sys_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, S_CPOS);
        
        // 注意：位置信息会通过串口中断接收并由parse_x_motor_data和parse_y_motor_data处理
        // 标记位置已保存在有效位置数据被解析后设置
        my_printf(&huart1, "正在读取初始位置...\r\n");
    }
}

// 指令处理函数
//void process_command(const char* cmd, uint16_t len)
//{
//    // 处理basic模块的复位指令
//    if (strncmp(cmd, "basic_reset", 11) == 0)
//    {
//        int result = basic_start_reset();
//        if (result == 0) {
//            my_printf(&huart1, "Basic reset command executed successfully\r\n");
//        } else {
//            my_printf(&huart1, "Basic reset command failed: %d\r\n", result);
//        }
//    }
//    // 处理basic模块的模式切换指令
//    else if (strncmp(cmd, "basic_mode_", 11) == 0)
//    {
//        /*
//        if (strncmp(cmd + 11, "track", 5) == 0) {
//            basic_set_mode(BASIC_MODE_TRACKING);
//            my_printf(&huart1, "Switched to TRACKING mode\r\n");
//        }
//        */
//        if (strncmp(cmd + 11, "reset", 5) == 0) {
//            basic_set_mode(BASIC_MODE_RESET);
//            my_printf(&huart1, "Switched to RESET mode\r\n");
//        }
//        else if (strncmp(cmd + 11, "screen_edge", 11) == 0) {
//            basic_set_mode(BASIC_MODE_SCREEN_EDGE);
//            my_printf(&huart1, "Switched to SCREEN_EDGE (Four-Point Tracking) mode\r\n");
//            my_printf(&huart1, "Ready for four-point tracking. Waiting for MaixCam coordinates...\r\n");
//        }
//        else if (strncmp(cmd + 11, "a4_tape", 7) == 0) {
//            basic_set_mode(BASIC_MODE_A4_TAPE);
//            my_printf(&huart1, "Switched to A4_TAPE mode\r\n");
//        }
//        else if (strncmp(cmd + 11, "rotated_a4", 10) == 0) {
//            basic_set_mode(BASIC_MODE_ROTATED_A4);
//            my_printf(&huart1, "Switched to ROTATED_A4 mode\r\n");
//        }
//        else if (strncmp(cmd + 11, "auto_track", 10) == 0) {
//            basic_set_mode(BASIC_MODE_AUTO_TRACKING);
//            my_printf(&huart1, "Switched to AUTO_TRACKING mode\r\n");
//        }
//        else {
//            my_printf(&huart1, "Unknown basic mode: %s\r\n", cmd + 11);
//            my_printf(&huart1, "Available modes: reset, screen_edge, a4_tape, rotated_a4, auto_track\r\n");
//            my_printf(&huart1, "Note: tracking mode is disabled, use auto_track instead\r\n");
//        }
//    }
//    // 更新原点位置 - 已注释，专注复位功能
//    /*
//    else if (strncmp(cmd, "basic_update_origin", 19) == 0)
//    {
//        basic_update_origin();
//        my_printf(&huart1, "Origin update command executed\r\n");
//    }
//    */
//    // 查询basic模块状态
//    else if (strncmp(cmd, "basic_status", 12) == 0)
//    {
//        BasicControlMode_t mode = basic_get_mode();
//        ResetStatus_t reset_status = basic_get_reset_status();

//        my_printf(&huart1, "Basic Status - Mode: %d, Reset Status: %d\r\n", mode, reset_status);

////        if (latest_red_laser_coord.isValid) {
////            float distance = basic_calculate_distance(
////                latest_red_laser_coord.x, latest_red_laser_coord.y,
////                g_target_red_x, g_target_red_y);
////            my_printf(&huart1, "Current: (%d,%d), Target: (%d,%d), Distance: %.1f px\r\n",
////                      latest_red_laser_coord.x, latest_red_laser_coord.y,
////                      g_target_red_x, g_target_red_y, distance);
////        }

//        // 显示详细的矩形信息
//        my_printf(&huart1, "=== Rectangle Data Status ===\r\n");
//        if (latest_rect_coord.isValid) {
//            my_printf(&huart1, "Rectangle Status: DETECTED\r\n");
//            my_printf(&huart1, "Rectangle Coordinates:\r\n");
//            my_printf(&huart1, "  Point 1: (%d, %d)\r\n", latest_rect_coord.x[0], latest_rect_coord.y[0]);
//            my_printf(&huart1, "  Point 2: (%d, %d)\r\n", latest_rect_coord.x[1], latest_rect_coord.y[1]);
//            my_printf(&huart1, "  Point 3: (%d, %d)\r\n", latest_rect_coord.x[2], latest_rect_coord.y[2]);
//            my_printf(&huart1, "  Point 4: (%d, %d)\r\n", latest_rect_coord.x[3], latest_rect_coord.y[3]);

//            // 计算矩形中心
//            int center_x = (latest_rect_coord.x[0] + latest_rect_coord.x[1] +
//                           latest_rect_coord.x[2] + latest_rect_coord.x[3]) / 4;
//            int center_y = (latest_rect_coord.y[0] + latest_rect_coord.y[1] +
//                           latest_rect_coord.y[2] + latest_rect_coord.y[3]) / 4;
//            my_printf(&huart1, "Rectangle Center: (%d, %d)\r\n", center_x, center_y);
//        } else {
//            my_printf(&huart1, "Rectangle Status: NOT DETECTED\r\n");
//            my_printf(&huart1, "Rectangle Coordinates: N/A\r\n");
//        }

//        // 显示绿色激光信息
//        my_printf(&huart1, "=== Green Laser Data ===\r\n");
//        if (latest_green_laser_coord.isValid) {
//            my_printf(&huart1, "Green Laser Status: DETECTED\r\n");
//            my_printf(&huart1, "Green Laser Position: (%d, %d)\r\n",
//                      latest_green_laser_coord.x, latest_green_laser_coord.y);
//        } else {
//            my_printf(&huart1, "Green Laser Status: NOT DETECTED\r\n");
//        }

//        // 显示按键功能说明
//        my_printf(&huart1, "=== Key Functions ===\r\n");
//        my_printf(&huart1, "PE0 (KEY1) - Motor position reset\r\n");
//        my_printf(&huart1, "PE1 (KEY2) - Smart auto tracking\r\n");
//        my_printf(&huart1, "PE2 (KEY3) - Check status\r\n");
//        my_printf(&huart1, "PE3 (KEY4) - Reserved (tracking mode disabled)\r\n");

//        // 显示自动循迹状态
//        AutoTrackingStatus_t auto_status = basic_get_auto_tracking_status();
//        my_printf(&huart1, "=== Auto Tracking Status ===\r\n");
//        switch (auto_status) {
//            case AUTO_TRACK_IDLE:
//                my_printf(&huart1, "Auto Tracking: IDLE (Ready to start)\r\n");
//                break;
//            case AUTO_TRACK_FIND_NEAREST:
//                my_printf(&huart1, "Auto Tracking: Finding nearest corner\r\n");
//                break;
//            case AUTO_TRACK_MOVE_TO_START:
//                my_printf(&huart1, "Auto Tracking: Moving to start point\r\n");
//                break;
//            case AUTO_TRACK_TRACKING:
//                my_printf(&huart1, "Auto Tracking: Tracking in progress\r\n");
//                break;
//            case AUTO_TRACK_COMPLETED:
//                my_printf(&huart1, "Auto Tracking: Completed\r\n");
//                break;
//            case AUTO_TRACK_RESETTING:
//                my_printf(&huart1, "Auto Tracking: Auto resetting\r\n");
//                break;
//            default:
//                my_printf(&huart1, "Auto Tracking: Unknown status\r\n");
//                break;
//        }
//    }
//    // 处理原有的reset指令（保持兼容性）
//    else if (strncmp(cmd, "reset", 5) == 0)
//    {
//        // 处理复位指令
//        process_reset_command();
//    }
//    // 四点循迹快捷启动命令
//    else if (strncmp(cmd, "four_point_start", 16) == 0)
//    {
//        basic_set_mode(BASIC_MODE_SCREEN_EDGE);
//        my_printf(&huart1, "Four-Point Tracking Started!\r\n");
//        my_printf(&huart1, "System ready for pencil line tracking. Waiting for MaixCam coordinates...\r\n");
//        my_printf(&huart1, "Expected: 30-second clockwise movement around screen edges\r\n");
//    }
//    // 智能自动循迹启动命令 (推荐使用PE1按键)
//    else if (strncmp(cmd, "auto_track_start", 16) == 0)
//    {
//        my_printf(&huart1, "Note: Recommend using PE1 key for auto tracking\r\n");
//        int result = basic_start_auto_tracking();
//        if (result == 0) {
//            my_printf(&huart1, "Smart Auto Tracking Started Successfully!\r\n");
//            my_printf(&huart1, "Features:\r\n");
//            my_printf(&huart1, "- Automatic nearest corner detection\r\n");
//            my_printf(&huart1, "- Intelligent clockwise tracking\r\n");
//            my_printf(&huart1, "- Automatic reset after completion\r\n");
//            my_printf(&huart1, "- Expected completion time: 30 seconds\r\n");
//        } else {
//            my_printf(&huart1, "Failed to start auto tracking. Check rectangle and laser data.\r\n");
//        }
//    }
//    // 停止自动循迹命令
//    else if (strncmp(cmd, "auto_track_stop", 15) == 0)
//    {
//        basic_stop_auto_tracking();
//    }
//    // set(x,y)指令 - 设置目标点 - 已注释，专注复位功能
//    /*
//    else if (strncmp(cmd, "set(", 4) == 0)
//    {
//        int target_x, target_y;
//        // 尝试解析坐标值
//        if (sscanf(cmd, "set(%d,%d)", &target_x, &target_y) == 2)
//        {
//            // 设置PID目标值
//            my_printf(&huart1, "Set target position: (%d, %d)\r\n", target_x, target_y);
//        }
//        else
//        {
//            my_printf(&huart1, "set指令格式错误，应为 set(x,y)\r\n");
//        }
//    }
//    */
//    else
//    {
//        my_printf(&huart1, "Unknown command: %s\r\n", cmd);
//    }
//}

void uart_proc(void)
{
	uint16_t length_x, length_y, length_pi,length_test;
	Emm_V5_Response_t resp_x, resp_y; // 为X轴和Y轴分别创建独立的响应结构体
	// 处理X轴电机数据
	length_x = rt_ringbuffer_data_len(&ringbuffer_x);
//	my_printf(&huart1, "abc%d\n",length_x);
	if (length_x > 0)
	{
		rt_ringbuffer_get(&ringbuffer_x, output_buffer_x, length_x);
		output_buffer_x[length_x] = '\0';
//		my_printf(&huart1, "%d\n",length_x);
		// 解析X轴数据
		if (Emm_V5_Parse_Response(output_buffer_x, length_x, &resp_x))
		{
			parse_x_motor_data(&resp_x);
			//my_printf(&huart1,"id:%d",resp_x.addr);  // 注释掉调试打印
		}
		else
		{
			my_printf(&huart1, "X轴数据解析失败!\r\n");
		}

		memset(output_buffer_x, 0, length_x);
	}

	// 处理Y轴电机数据
	length_y = rt_ringbuffer_data_len(&ringbuffer_y);
	if (length_y > 0)
	{
		rt_ringbuffer_get(&ringbuffer_y, output_buffer_y, length_y);
		output_buffer_y[length_y] = '\0';

		// 解析Y轴数据
		if (Emm_V5_Parse_Response(output_buffer_y, length_y, &resp_y))
		{
			parse_y_motor_data(&resp_y);
		}
		else
		{
			// 检查是否是MaixCam数据误发到Y轴串口 - PI通信功能已注释
			/*
			if (strncmp((char*)output_buffer_y, "red:", 4) == 0 ||
			    strncmp((char*)output_buffer_y, "gre:", 4) == 0 ||
			    strncmp((char*)output_buffer_y, "rect:", 5) == 0 ||
			    strncmp((char*)output_buffer_y, "point:", 6) == 0)
			{
				my_printf(&huart1, "MaixCam data received on Y-axis port: '%s'\r\n", output_buffer_y);
				// 尝试解析MaixCam数据
				int result = pi_parse_data((char*)output_buffer_y);
				if (result == 0) {
					my_printf(&huart1, "MaixCam data parsed successfully on Y-axis port\r\n");
				} else {
					my_printf(&huart1, "MaixCam data parse failed: %d\r\n", result);
				}
			}
			else
			{
			*/
				my_printf(&huart1, "Y轴数据解析失败! 接收数据: '%s'\r\n", output_buffer_y);
			//}
		}

		memset(output_buffer_y, 0, length_y);
	}

	// 在数据处理完成后执行限幅检查
//	check_motor_angle_limits();
	
	// 处理MaixCam数据 - 已迁移到UART5 (ringbuffer_test)
	// 保留此代码以防需要其他用途
	/*
	length_pi = rt_ringbuffer_data_len(&ringbuffer_pi);
	if(length_pi > 0)
	{
		my_printf(&huart1,"MaixCam ringbuffer has %d bytes\r\n", length_pi);

		rt_ringbuffer_get(&ringbuffer_pi, output_buffer_pi, length_pi);
        // 逐字节遍历从ringbuffer中取出的数据
        for (int i = 0; i < length_pi; i++)
        {
            char current_char = output_buffer_pi[i];

            // 将当前字符添加到行缓冲区，同时检查是否会溢出
            // 减去 1 是为了给字符串末尾的空终止符留出空间
            if (line_buffer_idx < sizeof(line_buffer) - 1)
            {
                line_buffer[line_buffer_idx++] = current_char;
            }
            else
            {
                // 如果行缓冲区满了，但是没有收到换行符，说明当前行太长或者格式错误
                // 此时可以清空缓冲区并返回错误，或者进行其他错误处理
                my_printf(&huart1, "Error: Line buffer overflow without newline. Discarding line.\r\n");
                line_buffer_idx = 0; // 清空缓冲区，避免影响后续数据
                continue; // 跳过此字符，等待下一个可能的帧头
            }

            // 如果收到换行符 ('\n')，表示一个完整的帧结束
            // 或者如果收到回车符 ('\r')，在某些系统上它可能也表示行结束，或者与 \n 组合成 \r\n
            // 这里我们主要检查 \n
            if (current_char == '\n')
            {
                line_buffer[line_buffer_idx] = '\0'; // 在行末添加空终止符

                my_printf(&huart1, "Processing received line: '%s'\r\n", line_buffer); // 调试打印每一行

                // 将完整的行数据传递给 pi_parse_data 进行解析
                int result = pi_parse_data(line_buffer);

                if (result != 0)
				{
                    my_printf(&huart1, "pi_parse_data returned error %d for line: '%s'\r\n", result, line_buffer);
                }

                line_buffer_idx = 0; // 重置行缓冲区索引，准备接收下一行数据
            }
        }

        // 不需要 memset(output_buffer_pi, 0, length_pi);
        // 因为数据已经从 ringbuffer 中取出并处理，output_buffer_pi 只是一个临时工作区
    }
	*/

    // MaixCam数据处理 - PI通信功能已注释
    /*
    length_test = rt_ringbuffer_data_len(&ringbuffer_test);
	if(length_test > 0)
	{
		my_printf(&huart1,"MaixCam ringbuffer has %d bytes\r\n", length_test);

		rt_ringbuffer_get(&ringbuffer_test, output_buffer_test, length_test);
        // 逐字节遍历从ringbuffer中取出的数据
        for (int i = 0; i < length_test; i++)
        {
            char current_char = output_buffer_test[i];

            // 将当前字符添加到行缓冲区，同时检查是否会溢出
            // 减去 1 是为了给字符串末尾的空终止符留出空间
            if (line_buffer_idx < sizeof(line_buffer) - 1)
            {
                line_buffer[line_buffer_idx++] = current_char;
            }
            else
            {
                // 如果行缓冲区满了，但是没有收到换行符，说明当前行太长或者格式错误
                // 此时可以清空缓冲区并返回错误，或者进行其他错误处理
                my_printf(&huart1, "Error: Line buffer overflow without newline. Discarding line.\r\n");
                line_buffer_idx = 0; // 清空缓冲区，避免影响后续数据
                continue; // 跳过此字符，等待下一个可能的帧头
            }

            // 如果收到换行符 ('\n')，表示一个完整的帧结束
            // 或者如果收到回车符 ('\r')，在某些系统上它可能也表示行结束，或者与 \n 组合成 \r\n
            // 这里我们主要检查 \n
            if (current_char == '\n')
            {
                line_buffer[line_buffer_idx] = '\0'; // 在行末添加空终止符

                my_printf(&huart1, "Processing received line: '%s'\r\n", line_buffer); // 调试打印每一行

                // 将完整的行数据传递给 pi_parse_data 进行解析
               // int result = pi_parse_data(line_buffer);

                if (result != 0)
				{
                    my_printf(&huart1, "pi_parse_data returned error %d for line: '%s'\r\n", result, line_buffer);
                }

                line_buffer_idx = 0; // 重置行缓冲区索引，准备接收下一行数据
            }
        }

        // 不需要 memset(output_buffer_pi, 0, length_pi);
        // 因为数据已经从 ringbuffer 中取出并处理，output_buffer_pi 只是一个临时工作区
    }
    */

}

/**
 * @brief 处理串口1接收到的命令数据
 * @param data 接收到的数据
 * @param len 数据长度
 */
void uart1_process_command(uint8_t *data, uint16_t len)
{
    static uint8_t cmd_index = 0;

    // 添加调试信息：显示接收到的字符
    my_printf(&huart1, "DEBUG: uart1_process_command called, char=0x%02X ('%c')\r\n", data[0], data[0]);

    for (uint16_t i = 0; i < len; i++) {
        char ch = (char)data[i];

        // 处理回车换行
        if (ch == '\r' || ch == '\n') {
            if (cmd_index > 0) {
                g_cmd_buffer[cmd_index] = '\0';
                g_cmd_ready = 1;
                my_printf(&huart1, "\r\nDEBUG: Command ready: '%s'\r\n", g_cmd_buffer);
                cmd_index = 0;
            }
        }
        // 处理退格
        else if (ch == '\b' || ch == 127) {
            if (cmd_index > 0) {
                cmd_index--;
                my_printf(&huart1, "\b \b"); // 回显退格
            }
        }
        // 处理普通字符
        else if (ch >= 32 && ch <= 126) {
            if (cmd_index < CMD_BUFFER_SIZE - 1) {
                g_cmd_buffer[cmd_index++] = ch;
                my_printf(&huart1, "%c", ch); // 回显字符
            }
        }
    }
}



