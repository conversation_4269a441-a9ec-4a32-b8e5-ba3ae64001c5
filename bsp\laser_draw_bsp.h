#ifndef __LASER_DRAW_BSP_H__
#define __LASER_DRAW_BSP_H__

#include "bsp_system.h"
#include <math.h>

// 画图系统配置
#define CANVAS_WIDTH_MM     50.0f    // 画布宽度(毫米)
#define CANVAS_HEIGHT_MM    50.0f    // 画布高度(毫米)
#define DRAW_SPEED_DEFAULT  10        // 默认绘图速度(百分比)
#define MOVE_SPEED_DEFAULT  10        // 默认移动速度(百分比)
#define STEP_RESOLUTION     0.5f      // 步进分辨率(毫米)

// 激光控制引脚定义
#define LASER_PIN           GPIO_PIN_0
#define LASER_PORT          GPIOA

// 绘图状态枚举
typedef enum {
    DRAW_STATE_IDLE = 0,        // 空闲状态
    DRAW_STATE_MOVING,          // 移动中(激光关闭)
    DRAW_STATE_DRAWING,         // 绘图中(激光开启)
    DRAW_STATE_COMPLETED,       // 绘图完成
    DRAW_STATE_ERROR            // 错误状态
} DrawState_t;

// 坐标点结构体
typedef struct {
    float x;                    // X坐标(毫米)
    float y;                    // Y坐标(毫米)
    uint8_t laser_on;           // 激光状态(1=开启, 0=关闭)
} DrawPoint_t;

// 绘图路径结构体
typedef struct {
    DrawPoint_t *points;        // 路径点数组
    uint16_t point_count;       // 路径点数量
    uint16_t current_index;     // 当前执行的点索引
    uint8_t is_active;          // 路径是否激活
} DrawPath_t;

// 绘图控制结构体
typedef struct {
    DrawState_t state;          // 当前状态
    float current_x;            // 当前X位置(毫米)
    float current_y;            // 当前Y位置(毫米)
    uint8_t draw_speed;         // 绘图速度(百分比)
    uint8_t move_speed;         // 移动速度(百分比)
    uint8_t laser_state;        // 激光当前状态
    DrawPath_t current_path;    // 当前执行的路径
} LaserDrawControl_t;

// 预定义图形枚举
typedef enum {
    SHAPE_SQUARE = 0,           // 正方形
    SHAPE_CIRCLE,               // 圆形
    SHAPE_TRIANGLE,             // 三角形
    SHAPE_STAR,                 // 五角星
    SHAPE_HEART,                // 心形
    SHAPE_CUSTOM                // 自定义路径
} ShapeType_t;

// 图形参数结构体
typedef struct {
    float center_x;             // 中心X坐标
    float center_y;             // 中心Y坐标
    float size;                 // 图形大小
    float rotation;             // 旋转角度(度)
} ShapeParams_t;

// 函数声明
void LaserDraw_Init(void);
void LaserDraw_Process(void);

// 基础移动函数
int LaserDraw_MoveTo(float x, float y, uint8_t laser_on);
int LaserDraw_MoveRelative(float dx, float dy, uint8_t laser_on);
int LaserDraw_SetSpeed(uint8_t draw_speed, uint8_t move_speed);

// 路径控制函数
int LaserDraw_StartPath(DrawPoint_t *points, uint16_t count);
int LaserDraw_StopPath(void);
int LaserDraw_PausePath(void);
int LaserDraw_ResumePath(void);

// 预定义图形绘制函数
int LaserDraw_DrawShape(ShapeType_t shape, ShapeParams_t *params);
int LaserDraw_DrawSquare(float center_x, float center_y, float size);
int LaserDraw_DrawCircle(float center_x, float center_y, float radius);
int LaserDraw_DrawTriangle(float center_x, float center_y, float size);
int LaserDraw_DrawStar(float center_x, float center_y, float size);
int LaserDraw_DrawHeart(float center_x, float center_y, float size);

// 直线和曲线绘制函数
int LaserDraw_DrawLine(float start_x, float start_y, float end_x, float end_y);
int LaserDraw_DrawArc(float center_x, float center_y, float radius, 
                      float start_angle, float end_angle);

// 文字绘制函数(简单字符)
int LaserDraw_DrawChar(char ch, float x, float y, float size);
int LaserDraw_DrawString(const char *str, float x, float y, float size);

// 状态查询函数
DrawState_t LaserDraw_GetState(void);
float LaserDraw_GetCurrentX(void);
float LaserDraw_GetCurrentY(void);
uint8_t LaserDraw_IsLaserOn(void);
uint16_t LaserDraw_GetPathProgress(void);

// 校准和测试函数
int LaserDraw_Calibrate(void);
int LaserDraw_TestPattern(void);
int LaserDraw_Home(void);

// 安全和限位函数
int LaserDraw_CheckBounds(float x, float y);
int LaserDraw_EmergencyStop(void);

// 外部变量声明
extern LaserDrawControl_t g_laser_draw;

#endif /* __LASER_DRAW_BSP_H__ */
