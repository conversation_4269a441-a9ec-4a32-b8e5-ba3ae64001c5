#include "laser_draw_cmd.h"
#include <string.h>
#include <stdlib.h>

// 全局变量
char g_cmd_buffer[CMD_BUFFER_SIZE] = {0};
uint8_t g_cmd_ready = 0;

// 命令表
static const Command_t command_table[] = {
    {CMD_HELP,          "help",      {0}, 0, "Show help information"},
    {CMD_HOME,          "home",      {0}, 0, "Move to origin (0,0)"},
    {CMD_MOVE,          "move",      {0}, 2, "move x y - Move to position"},
    {CMD_LASER_ON,      "laser_on",  {0}, 0, "Turn on laser"},
    {CMD_LASER_OFF,     "laser_off", {0}, 0, "Turn off laser"},
    {CMD_DRAW_LINE,     "line",      {0}, 4, "line x1 y1 x2 y2 - Draw line"},
    {CMD_DRAW_SQUARE,   "square",    {0}, 3, "square x y size - Draw square"},
    {<PERSON><PERSON>_DRAW_CIRCLE,   "circle",    {0}, 3, "circle x y radius - Draw circle"},
    {CMD_DRAW_TRIANGLE, "triangle",  {0}, 3, "triangle x y size - Draw triangle"},
    {CMD_DRAW_STAR,     "star",      {0}, 3, "star x y size - Draw star"},
    {CMD_DRAW_HEART,    "heart",     {0}, 3, "heart x y size - Draw heart"},
    {CMD_DRAW_ARC,      "arc",       {0}, 5, "arc x y radius start_angle end_angle"},
    {CMD_SET_SPEED,     "speed",     {0}, 2, "speed draw_speed move_speed"},
    {CMD_STATUS,        "status",    {0}, 0, "Show current status"},
    {CMD_CALIBRATE,     "calibrate", {0}, 0, "Calibrate system"},
    {CMD_TEST,          "test",      {0}, 0, "Draw test pattern"},
    {CMD_STOP,          "stop",      {0}, 0, "Stop current operation"},
    {CMD_EMERGENCY_STOP,"estop",     {0}, 0, "Emergency stop"},
    {CMD_TRACK_START,   "track_start", {0}, 0, "Start laser tracking"},
    {CMD_TRACK_STOP,    "track_stop",  {0}, 0, "Stop laser tracking"},
    {CMD_TRACK_STATUS,  "track_status",{0}, 0, "Show tracking status"}
};

static const uint8_t command_count = sizeof(command_table) / sizeof(Command_t);

/**
 * @brief 激光绘图命令系统初始化
 */
void LaserDrawCmd_Init(void)
{
    memset(g_cmd_buffer, 0, CMD_BUFFER_SIZE);
    g_cmd_ready = 0;
    
    my_printf(&huart1, "\r\n=== Laser Draw Command System ===\r\n");
    my_printf(&huart1, "Type 'help' for available commands\r\n");
    my_printf(&huart1, "Ready for commands...\r\n");
}

/**
 * @brief 命令处理函数(需要在主循环中调用)
 */
void LaserDrawCmd_Process(void)
{
    if (g_cmd_ready) {
        ParseResult_t result;

        // 解析命令
        if (LaserDrawCmd_ParseCommand(g_cmd_buffer, &result) == 0) {
            // 执行命令
            LaserDrawCmd_ExecuteCommand(&result);
        } else {
            my_printf(&huart1, "Error: Invalid command format\r\n");
            my_printf(&huart1, "Type 'help' for available commands\r\n");
        }

        // 清空缓冲区
        memset(g_cmd_buffer, 0, CMD_BUFFER_SIZE);
        g_cmd_ready = 0;
    }
}

/**
 * @brief 解析命令字符串
 * @param cmd_str 命令字符串
 * @param result 解析结果
 * @return 0:成功, -1:失败
 */
int LaserDrawCmd_ParseCommand(const char *cmd_str, ParseResult_t *result)
{
    if (!cmd_str || !result) return -1;
    
    char cmd_copy[CMD_BUFFER_SIZE];
    strncpy(cmd_copy, cmd_str, CMD_BUFFER_SIZE - 1);
    cmd_copy[CMD_BUFFER_SIZE - 1] = '\0';
    
    // 分割命令和参数
    char *token = strtok(cmd_copy, " \t\r\n");
    if (!token) return -1;
    
    // 查找命令
    result->type = CMD_UNKNOWN;
    for (uint8_t i = 0; i < command_count; i++) {
        if (strcmp(token, command_table[i].name) == 0) {
            result->type = command_table[i].type;
            break;
        }
    }
    
    if (result->type == CMD_UNKNOWN) {
        return -1;
    }
    
    // 解析参数
    result->param_count = 0;
    while ((token = strtok(NULL, " \t\r\n")) != NULL && result->param_count < MAX_CMD_PARAMS) {
        result->params[result->param_count] = atof(token);
        result->param_count++;
    }
    
    result->error_code = 0;
    return 0;
}

/**
 * @brief 执行命令
 * @param result 解析结果
 * @return 0:成功, -1:失败
 */
int LaserDrawCmd_ExecuteCommand(ParseResult_t *result)
{
    if (!result) return -1;
    
    int ret = 0;
    
    switch (result->type) {
        case CMD_HELP:
            LaserDrawCmd_ShowHelp();
            break;
            
        case CMD_HOME:
            ret = LaserDraw_Home();
            break;
            
        case CMD_MOVE:
            ret = LaserDrawCmd_HandleMove(result->params, result->param_count);
            break;
  
        case CMD_DRAW_LINE:
            ret = LaserDrawCmd_HandleLine(result->params, result->param_count);
            break;
            
        case CMD_DRAW_SQUARE:
            ret = LaserDrawCmd_HandleSquare(result->params, result->param_count);
            break;
            
        case CMD_DRAW_CIRCLE:
            ret = LaserDrawCmd_HandleCircle(result->params, result->param_count);
            break;
            
        case CMD_DRAW_TRIANGLE:
            ret = LaserDrawCmd_HandleTriangle(result->params, result->param_count);
            break;
            
        case CMD_DRAW_STAR:
            ret = LaserDrawCmd_HandleStar(result->params, result->param_count);
            break;
            
        case CMD_DRAW_HEART:
            ret = LaserDrawCmd_HandleHeart(result->params, result->param_count);
            break;
            
        case CMD_DRAW_ARC:
            ret = LaserDrawCmd_HandleArc(result->params, result->param_count);
            break;
            
        case CMD_SET_SPEED:
            ret = LaserDrawCmd_HandleSpeed(result->params, result->param_count);
            break;
            
        case CMD_STATUS:
            LaserDrawCmd_ShowStatus();
            break;
            
        case CMD_CALIBRATE:
            ret = LaserDraw_Calibrate();
            break;
            
        case CMD_TEST:
            ret = LaserDraw_TestPattern();
            break;
            
        case CMD_STOP:
            ret = LaserDraw_StopPath();
            break;
            
        case CMD_EMERGENCY_STOP:
            ret = LaserDraw_EmergencyStop();
            break;

        case CMD_TRACK_START:
            ret = LaserDrawCmd_HandleTrackStart(result->params, result->param_count);
            break;

        case CMD_TRACK_STOP:
            ret = LaserDrawCmd_HandleTrackStop(result->params, result->param_count);
            break;

        case CMD_TRACK_STATUS:
            LaserDrawCmd_ShowTrackStatus();
            break;

        default:
            my_printf(&huart1, "Error: Unknown command\r\n");
            ret = -1;
            break;
    }
    
    if (ret == 0) {
        my_printf(&huart1, "OK\r\n");
    } else {
        my_printf(&huart1, "ERROR\r\n");
    }
    
    return ret;
}

/**
 * @brief 显示帮助信息
 */
void LaserDrawCmd_ShowHelp(void)
{
    my_printf(&huart1, "\r\n=== Laser Draw Commands ===\r\n");

    for (uint8_t i = 0; i < command_count; i++) {
        my_printf(&huart1, "%-12s - %s\r\n",
                  command_table[i].name,
                  command_table[i].description);
    }

    my_printf(&huart1, "\r\nExamples:\r\n");
    my_printf(&huart1, "move 50 50        - Move to (50,50)\r\n");
    my_printf(&huart1, "line 0 0 50 50    - Draw line from (0,0) to (50,50)\r\n");
    my_printf(&huart1, "circle 50 50 20   - Draw circle at (50,50) with radius 20\r\n");
    my_printf(&huart1, "square 50 50 30   - Draw square at (50,50) with size 30\r\n");
    my_printf(&huart1, "speed 60 80       - Set draw speed 60%%, move speed 80%%\r\n");
    my_printf(&huart1, "track_start       - Start laser tracking\r\n");
    my_printf(&huart1, "track_stop        - Stop laser tracking\r\n");
    my_printf(&huart1, "track_status      - Show tracking status\r\n");
    my_printf(&huart1, "\r\nCanvas size: %.1f x %.1f mm\r\n",
              CANVAS_WIDTH_MM, CANVAS_HEIGHT_MM);
}

/**
 * @brief 显示状态信息
 */
void LaserDrawCmd_ShowStatus(void)
{
    const char* state_names[] = {
        "IDLE", "MOVING", "DRAWING", "COMPLETED", "ERROR"
    };
    
    DrawState_t state = LaserDraw_GetState();
    
    my_printf(&huart1, "\r\n=== System Status ===\r\n");
    my_printf(&huart1, "State: %s\r\n", state_names[state]);
    my_printf(&huart1, "Position: (%.1f, %.1f) mm\r\n", 
              LaserDraw_GetCurrentX(), LaserDraw_GetCurrentY());
    my_printf(&huart1, "Laser: %s\r\n", LaserDraw_IsLaserOn() ? "ON" : "OFF");
    my_printf(&huart1, "Canvas: %.1f x %.1f mm\r\n", 
              CANVAS_WIDTH_MM, CANVAS_HEIGHT_MM);
}

// 命令处理函数实现

/**
 * @brief 处理移动命令
 */
int LaserDrawCmd_HandleMove(float *params, uint8_t count)
{
    if (count < 2) {
        my_printf(&huart1, "Error: move requires 2 parameters (x y)\r\n");
        return -1;
    }
    
    return LaserDraw_MoveTo(params[0], params[1], 0);
}

/**
 * @brief 处理直线命令
 */
int LaserDrawCmd_HandleLine(float *params, uint8_t count)
{
    if (count < 4) {
        my_printf(&huart1, "Error: line requires 4 parameters (x1 y1 x2 y2)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawLine(params[0], params[1], params[2], params[3]);
}

/**
 * @brief 处理正方形命令
 */
int LaserDrawCmd_HandleSquare(float *params, uint8_t count)
{
    if (count < 3) {
        my_printf(&huart1, "Error: square requires 3 parameters (x y size)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawSquare(params[0], params[1], params[2]);
}

/**
 * @brief 处理圆形命令
 */
int LaserDrawCmd_HandleCircle(float *params, uint8_t count)
{
    if (count < 3) {
        my_printf(&huart1, "Error: circle requires 3 parameters (x y radius)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawCircle(params[0], params[1], params[2]);
}

/**
 * @brief 处理三角形命令
 */
int LaserDrawCmd_HandleTriangle(float *params, uint8_t count)
{
    if (count < 3) {
        my_printf(&huart1, "Error: triangle requires 3 parameters (x y size)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawTriangle(params[0], params[1], params[2]);
}

/**
 * @brief 处理五角星命令
 */
int LaserDrawCmd_HandleStar(float *params, uint8_t count)
{
    if (count < 3) {
        my_printf(&huart1, "Error: star requires 3 parameters (x y size)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawStar(params[0], params[1], params[2]);
}

/**
 * @brief 处理心形命令
 */
int LaserDrawCmd_HandleHeart(float *params, uint8_t count)
{
    if (count < 3) {
        my_printf(&huart1, "Error: heart requires 3 parameters (x y size)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawHeart(params[0], params[1], params[2]);
}

/**
 * @brief 处理弧线命令
 */
int LaserDrawCmd_HandleArc(float *params, uint8_t count)
{
    if (count < 5) {
        my_printf(&huart1, "Error: arc requires 5 parameters (x y radius start_angle end_angle)\r\n");
        return -1;
    }
    
    return LaserDraw_DrawArc(params[0], params[1], params[2], params[3], params[4]);
}

/**
 * @brief 处理速度设置命令
 */
int LaserDrawCmd_HandleSpeed(float *params, uint8_t count)
{
    if (count < 2) {
        my_printf(&huart1, "Error: speed requires 2 parameters (draw_speed move_speed)\r\n");
        return -1;
    }
    
    return LaserDraw_SetSpeed((uint8_t)params[0], (uint8_t)params[1]);
}

// 跟踪相关命令处理函数实现

/**
 * @brief 处理开始跟踪命令
 */
int LaserDrawCmd_HandleTrackStart(float *params, uint8_t count)
{
    CameraData_SetTrackMode(TRACK_MODE_ACTIVE);
    my_printf(&huart1, "Laser tracking started\r\n");
    return 0;
}

/**
 * @brief 处理停止跟踪命令
 */
int LaserDrawCmd_HandleTrackStop(float *params, uint8_t count)
{
    CameraData_SetTrackMode(TRACK_MODE_IDLE);
    Laser_Off(); // 确保激光关闭
    my_printf(&huart1, "Laser tracking stopped\r\n");
    return 0;
}

/**
 * @brief 显示跟踪状态信息
 */
void LaserDrawCmd_ShowTrackStatus(void)
{
    const char* track_mode_names[] = {
        "IDLE", "ACTIVE", "TRACKING", "LOST"
    };

    TrackMode_t mode = CameraData_GetTrackMode();
    LaserPoint_t *point = CameraData_GetCurrentLaserPoint();
    CanvasData_t *canvas = CameraData_GetCanvasData();

    my_printf(&huart1, "\r\n=== Laser Tracking Status ===\r\n");
    my_printf(&huart1, "Track Mode: %s\r\n", track_mode_names[mode]);

    if (point->valid) {
        float physical_x, physical_y;
        if (CameraData_PixelToPhysical(point->x, point->y, &physical_x, &physical_y) == 0) {
            my_printf(&huart1, "Laser Point: pixel(%d,%d) -> physical(%.1f,%.1f)mm\r\n",
                      point->x, point->y, physical_x, physical_y);
        } else {
            my_printf(&huart1, "Laser Point: pixel(%d,%d) [OUT OF BOUNDS]\r\n",
                      point->x, point->y);
        }
        my_printf(&huart1, "Point Age: %ldms\r\n", HAL_GetTick() - point->timestamp);
    } else {
        my_printf(&huart1, "Laser Point: No valid data\r\n");
    }

    if (canvas->valid) {
        my_printf(&huart1, "Canvas: Valid (age: %ldms)\r\n", HAL_GetTick() - canvas->timestamp);
        my_printf(&huart1, "Canvas Points: (%d,%d) (%d,%d) (%d,%d) (%d,%d)\r\n",
                  canvas->points[0][0], canvas->points[0][1],
                  canvas->points[1][0], canvas->points[1][1],
                  canvas->points[2][0], canvas->points[2][1],
                  canvas->points[3][0], canvas->points[3][1]);
    } else {
        my_printf(&huart1, "Canvas: No valid data\r\n");
    }
}
