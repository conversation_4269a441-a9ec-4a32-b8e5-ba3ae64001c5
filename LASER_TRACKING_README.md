# 激光跟踪绘图系统使用说明

## 系统概述

本系统实现了基于MaixCam摄像头的激光点实时跟踪绘图功能。系统包含两个主要部分：

1. **MaixCam端**：检测紫色激光点位置，发送坐标数据
2. **STM32端**：接收坐标数据，控制步进电机和激光器进行跟踪绘图

## 功能特性

- 实时激光点检测和跟踪
- 像素坐标到物理坐标的自动转换
- 画布边界检查和安全保护
- 多种绘图模式支持
- 完整的命令行控制接口

## 坐标系统

### 摄像头坐标系
- 分辨率：320x240像素
- 画布区域：240x170像素
- 画布位置：(40,35) 到 (280,205)

### 物理坐标系
- 画布尺寸：50x50mm
- 坐标转换公式：
  - x_mm = (x_pixel - 40) * 50.0 / 240.0
  - y_mm = (y_pixel - 35) * 50.0 / 170.0

## 通信协议

### MaixCam -> STM32 数据格式

1. **激光点坐标**：`violet:(x,y)\n`
   - 示例：`violet:(160,120)\n`

2. **画布坐标**：`rect:(x1,y1,x2,y2,x3,y3,x4,y4)\n`
   - 示例：`rect:(40,35,280,35,280,205,40,205)\n`

## STM32命令接口

### 基础绘图命令
- `help` - 显示帮助信息
- `home` - 回到原点(0,0)
- `move x y` - 移动到指定位置
- `status` - 显示系统状态

### 激光跟踪命令
- `track_start` - 开始激光跟踪模式
- `track_stop` - 停止激光跟踪模式
- `track_status` - 显示跟踪状态信息

### 绘图命令
- `line x1 y1 x2 y2` - 绘制直线
- `square x y size` - 绘制正方形
- `circle x y radius` - 绘制圆形
- `triangle x y size` - 绘制三角形
- `star x y size` - 绘制五角星
- `heart x y size` - 绘制心形

### 系统控制命令
- `speed draw_speed move_speed` - 设置绘图和移动速度
- `calibrate` - 系统校准
- `test` - 绘制测试图案
- `stop` - 停止当前操作
- `estop` - 紧急停止

## 使用流程

### 1. 系统初始化
```
系统启动后会显示：
=== Laser Draw Command System ===
Type 'help' for available commands
Ready for commands...
Camera Data Processing Module Initialized
```

### 2. 启动激光跟踪
```
> track_start
Laser tracking started
OK
```

### 3. 检查跟踪状态
```
> track_status
=== Laser Tracking Status ===
Track Mode: ACTIVE
Laser Point: pixel(160,120) -> physical(25.0,25.0)mm
Point Age: 50ms
Canvas: Valid (age: 1000ms)
Canvas Points: (40,35) (280,35) (280,205) (40,205)
OK
```

### 4. 停止跟踪
```
> track_stop
Laser tracking stopped
OK
```

## 跟踪模式说明

### 跟踪状态
- **IDLE**：空闲状态，不进行跟踪
- **ACTIVE**：激活状态，等待激光点数据
- **TRACKING**：正在跟踪激光点
- **LOST**：激光点丢失

### 跟踪逻辑
1. 当检测到有效激光点时，系统自动移动到对应位置并开启激光
2. 当激光点丢失超过1秒时，系统关闭激光并进入丢失状态
3. 只有在画布范围内的激光点才会被跟踪

## 安全特性

- 画布边界检查：超出范围的坐标会被拒绝
- 激光自动关闭：跟踪丢失时自动关闭激光
- 紧急停止：`estop`命令可立即停止所有操作
- 超时保护：激光点数据超时自动关闭激光

## 调试信息

系统会输出以下调试信息：
- MaixCam数据接收状态
- 坐标转换结果
- 跟踪状态变化
- 错误和异常情况

## 故障排除

### 常见问题
1. **激光不跟踪**：检查是否执行了`track_start`命令
2. **坐标不准确**：检查画布校准是否正确
3. **激光频繁开关**：可能是激光点检测不稳定
4. **无法接收数据**：检查UART5连接和波特率设置

### 调试步骤
1. 使用`track_status`检查系统状态
2. 使用`status`检查激光绘图系统状态
3. 检查串口连接和数据传输
4. 验证坐标转换是否正确

## 技术参数

- 串口波特率：115200
- 跟踪更新频率：50Hz (20ms)
- 坐标精度：0.1mm
- 激光丢失超时：1000ms
- 画布尺寸：50x50mm
- 支持的激光颜色：紫色(violet)
