#include "schedule.h"

typedef struct {
	void (*task_func)(void);
	uint32_t rate_ms;
	uint32_t last_run;
}schedule_task_t;

uint8_t task_num;

static schedule_task_t schedule_task[] = {
//	{oled_proc,100,0},           // OLED显示功能 - 已注释
	{uart_proc,1,0},             // 串口通信功能 - 保留
	//{pi_proc,20,0},             // PI树莓派通信功能 - 已注释
	{key_proc,10,0},             // 按键处理功能 - 保留，每10ms检查一次
	{LaserDraw_Process,20,0},    // 激光绘图处理功能 - 新增，每20ms执行一次
	{LaserDrawCmd_Process,10,0}, // 激光绘图命令处理功能 - 修改为每10ms执行一次，提高响应速度
	{CameraData_ProcessTracking,20,0} // 摄像头跟踪处理功能 - 新增，每20ms执行一次

//	{motor_proc,20,0},           // 电机处理功能 - 已注释
//	{encoder_proc,20,0},         // 编码器处理功能 - 已注释
//	{gray_proc,20,0}             // 灰度传感器处理功能 - 已注释

};

/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void schedule_init(void)
{
	task_num = sizeof(schedule_task) / sizeof(schedule_task_t);
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void schedule_run(void)
{
    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 获取当前的系统时间（毫秒）
        uint32_t now_time = HAL_GetTick();

        // 检查当前时间是否达到任务的执行时间
        if (now_time >= schedule_task[i].rate_ms + schedule_task[i].last_run)
        {
            // 更新任务的上次运行时间为当前时间
            schedule_task[i].last_run = now_time;

            // 执行任务函数
            schedule_task[i].task_func();
        }
    }
}
